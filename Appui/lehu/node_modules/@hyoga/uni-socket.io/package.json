{"name": "@hyoga/uni-socket.io", "version": "1.0.1", "description": "适用于uni-app的socket.io封装，可用于uni-app、微信小程序", "keywords": ["mp-weixin", "uni-app", "websocket", "socket.io", "微信小程序"], "files": ["lib/", "dist/"], "main": "./dist/uni-socket.io.js", "scripts": {"prepublish": "rm -rf ./dist && npm run build", "build-dev": "webpack --config webpack.config.dev.js", "build": "webpack --config webpack.config.js"}, "dependencies": {"socket.io-client": "^2.1.1"}, "devDependencies": {"babel-core": "^6.24.1", "babel-eslint": "4.1.7", "babel-loader": "7.0.0", "babel-preset-env": "^1.7.0", "babel-preset-es2015": "6.24.1", "eslint-config-standard": "4.4.0", "eslint-plugin-standard": "1.3.1", "imports-loader": "^0.7.1", "strip-loader": "0.1.2", "webpack": "^4.12.0", "webpack-cli": "^3.0.8", "webpack-merge": "4.1.2", "webpack-stream": "3.2.0"}, "author": "<EMAIL>", "repository": {"type": "git", "url": "<EMAIL>:AspenLuoQiang/hyoga-uni-socket.io.git"}, "bugs": {"url": "https://github.com/AspenLuoQiang/hyoga-uni-socket.io/issues"}, "homepage": "https://github.com/AspenLuoQiang/hyoga-uni-socket.io", "license": "MIT"}