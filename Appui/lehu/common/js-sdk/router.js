import {
	RouterMount,
	createRouter
} from 'uni-simple-router';

const router = createRouter({
	platform: process.env.VUE_APP_PLATFORM,
	routes: [...ROUTES]
});
//全局路由前置守卫
const whiteList = ['/pages/index/loading', '/pages/login/login', '/pages/login/register']
router.beforeEach((to, from, next) => {
	// console.log(to,from,next);
	let token = uni.getStorageSync('access_token');
	if (token) {
		next()
	} else {
		if (whiteList.indexOf(to.path) !== -1) {
			next()
		} else {
			next({
				path: '/pages/login/login'
			})
		}
	}
	next();
});
// 全局路由后置守卫
router.afterEach((to, from) => {
	//console.log('来自哪里?',from,1)

})

export {
	router,
	RouterMount
}