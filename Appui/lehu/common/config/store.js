import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default new Vuex.Store({
	state: {
		memberData: '',
		initName: '',
		socket: false

	},
	mutations: {
		copy(state, cont) {
			//单一的改变某一个变量
			console.log(state)
			console.log(cont)
			state.memberData = cont;
		},
		change(state, contObj) {
			//通过传入的变量去改变对应的全局变量
			let str = contObj.str;
			let cont = contObj.cont;
			state[str] = cont;
		},
	},
	actions: {
		copeFun: function(context, mData) {
			context.commit('copy', mData)
		},
		openKefuLink(context) {
			const id = uni.getStorageSync('userinfo').id;
			const username = uni.getStorageSync('userinfo').username;
			const kefuUrl =
				`https://chat.linchat001.xyz/index/index/home?visiter_id=${id}&visiter_name=${username}&avatar=&business_id=1&groupid=1&special=1`;
			//uni.openUrl({ url: kefuUrl });
			if (process.env.UNI_PLATFORM === 'h5') {
				window.open(kefuUrl);
			} else {
				plus.runtime.openURL(kefuUrl);
			}
		}
	},
	getters: {

	}
})