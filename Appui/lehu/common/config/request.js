/*
 * @Author: Joson_ZZZ
 * @Date: 2023-07-19 15:48:42
 * @Description: Do not edit
 * @LastEditTime: 2024-09-09 22:44:01
 * @FilePath: \spidere:\AppUi\UniappProjects\lehu\common\config\request.js
 */
module.exports = (vm) => {
	// 初始化请求配置
	uni.$u.http.setConfig((config) => {
		const bestHttpDomain = uni.getStorageSync('bestHttpDomain');
		config.baseURL = bestHttpDomain || "http://*************:8787"; /* 使用最佳域名或默认本地测试地址 */
		config.sslVerify = true;
		config.timeout = 50000;
		config.header = {};
		config.custom.auth = true;
		config.custom.toast = true;
		return config
	})

	// 请求拦截
	uni.$u.http.interceptors.request.use(async (config) => { // 添加 async 关键字

		// 检查网络状态
		let networkType = 'none';
		// 同步获取当前网络状态
		uni.getNetworkType({
			success: function (res) {
				networkType = res.networkType;
			}
		});
		if (networkType === 'none') { // 没有网络连接
			uni.$u.toast('当前无网络连接，请检查您的网络设置');
			return Promise.reject({
				message: '当前无网络连接',
				config: config
			}); // 阻止请求继续
		}
		// 初始化请求拦截器时，会执行此方法，此时data为undefined，赋予默认{}
		//config.data = config.data || {}
		if (!config.data) {
			config.data = {}
		}
		// 根据custom参数中配置的是否需要token，添加对应的请求头
		if (config?.custom?.auth) {
			// 可以在此通过vm引用vuex中的变量，具体值在vm.$store.state中
			//config.header.token = vm.$store.state.userInfo.token
			// 若获取 token 是异步操作，可以如下操作
			// config.header.token = await someAsyncFunction()
			const token = uni.getStorageSync('access_token');
			if (token) {
				// 如果 token 存在，将其添加到请求头中
				config.header.Authorization = `Bearer ${token}`;
			}
		}
		//console.log('config',config);
		return config
	}, async (config) => { // 添加 async 关键字
		// 若有需要进行异步操作，可以在这里添加
		// let result = await someAsyncFunction()
		return Promise.reject(config)
	})

	// 响应拦截
	uni.$u.http.interceptors.response.use(async (response) => {
		const data = response.data;
		const custom = response.config?.custom;

		// 处理业务逻辑错误
		if (data.code !== 200) {
			if (data.code === 402) {
				setTimeout(() => {
					uni.reLaunch({
						url: 'pages/login/login'
					});
				}, 1000);
			}
			if (custom.toast !== false) {
				//uni.$u.toast(data.msg);
				return Promise.reject(data.msg)
			}
			// 确保业务逻辑错误被捕获到前端的 `catch`
			if (custom?.catch) {
				return Promise.reject(data)
			} else {
				return new Promise(() => { })
			}
		}

		return data === undefined ? {} : data;
	}, async (error) => {
		// 这里处理网络错误等情况
		if (error && error.response) {
			// 服务器返回的错误信息
			uni.$u.toast('服务器响应错误');
		} else if (error && error.code === 'ECONNABORTED') {
			// 请求超时的情况
			uni.$u.toast('请求超时，请稍后再试');
		} else {
			// 网络连接错误或其他情况
			uni.$u.toast('网络异常，请切换网络');
		}
		return Promise.reject(error);
	});
	//async (response) => { // 添加 async 关键字
	// 		// 若有需要进行异步操作，可以在这里添加
	// 		// let result = await someAsyncFunction()
	// 		return Promise.reject(response).catch()
	// 	})
}