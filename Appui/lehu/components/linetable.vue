<template>
	<view class="content" :style="{ backgroundColor: cententBg}">
		<view class="genaral-area">
			<view v-if="typeSrc==1" class="flex-box tc thead" :class="fixed == 'top' ? 'theadSticky' : ''">
				<view
					:class="item.$fixed == 'left' ? 'theadStickyleft' : ''" 
					:style="{ textAlign: align,border: '1px solid ' + borderBg,borderWidth:brwidth,color:textBg,backgroundColor: titleBg, flex: item.$flex }"
					class="item" 
					v-for="(item, index) in title"
					:key="index"
					v-if="isSave || !item.$operateList"
				>
					<text>{{ item.label }}</text>
				</view>
			</view>
			<view id="renderview">
				<view v-if="typeSrc==1" class="flex-box tc" v-for="(item, index) in tableData" :key="index" >
					<view :class="zItem.$fixed == 'left' ? 'theadStickyleft' : ''"  ref="tableRows"  class="item"  style="text-align: center; display: flex; justify-content: center;" 
					:style="{ textAlign: align,border: '1px solid ' + borderBg,borderWidth:brwidth,color:textBg, flex: zItem.$flex }"
											v-for="(zItem, zIndex) in title"
											:key="zIndex"
											v-if="isSave || !zItem.$operateList" >
											<template>
												<view :style="{ flex: item.$flex }">
													<view :class="item[zItem.value].state ? className(zItem.value) : ''" >
														{{ item[zItem.value].value || '-' }}
														 <text v-if='item[zItem.value].state' :class="reslion(zItem.value)"  :id="`T${index+1}_${zIndex}`">
														 </text>
													</view>
												</view>
											</template>
										</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			//这里是service层
		};
		
	},
	props: {
		typeSrc:{
			type: Number,
			default: 1
		},
		tableData: {
			type: Array,
			required: true
		},

		title: {
			type: Array,
			required: true
		},
		align: {
			type: String,
			default: 'center'
		},
		titleBg: {
			type: String,
			default: '#ffffff'
		},
		cententBg:{
			type:String,
			default: '#fff'
		},
		textBg:{
			type:String,
			default: '#fff'
		},
		borderBg:{
			type:String,
			default: '#fff'
		},
		brwidth:{
			type:String,
			default:'1px 1px 0 0'
		},
		isSave: {
			type: Boolean,
			default: true
		},
		fixed: {
			type: String,
			default: 'top'
		}
	},
	onLoad() {
		
		
	},
	mounted(){
		console.log('tableData',this.tableData);
	},
	methods: {
		className(index){
			if(index>0&&index<=12){
				return 'numActive lableNum';
			}
			// else if(index>8&&index<=10){
			// 	return 'sizeActive lableNum';
			// }
			// else{
			// 	return 'sdActive lableNum';
			// }
		},
		reslion(index){
			if(index>0&&index<=12){
				return 'line';
			}
			// else if(index>8&&index<=10){
			// 	return 'sizeline';
			// }
			// else{
			// 	return 'singline';
			// }
		},
		
		//事件触发
		pullEvent(event, data) {
			// this.$emit(event, data);
		}
	},
	
};
</script>
<script module="renderview" lang="renderjs">
	export default {
		data() {
		  return {
			eleData:{},
			sizeeleData:{},
			singeleData:{},
		  };
		},
		onLoad() {
			
		},
		mounted() {
		    this.$nextTick(() => {
				
				this.initialize();
		    });
		},

		methods: {
			initialize() {
				 this.$nextTick(() => {
					this.eleData = document.querySelectorAll(".line");
					this.sizeeleData = document.querySelectorAll(".sizeline");
					this.singeleData = document.querySelectorAll(".singline");
					this.drawLine(this.eleData);
					this.drawLine(this.sizeeleData);
					this.drawLine(this.singeleData);
					const now = new Date();
					// 监听 DOM 变化
					const observer = new MutationObserver(mutations => {
					  this.$nextTick(() => {
						  const newEleData = document.querySelectorAll(".line");
						  const newSizeeleData = document.querySelectorAll(".sizeline");
						  const newSingeleData = document.querySelectorAll(".singline");  
						  //newEleData[0].style.width='100px';
						  if (newEleData.length !== this.eleData.length) {
							newEleData.forEach(element => {
							  element.style = "";
							});
							this.drawLine(newEleData);
						  }
					
						  if (newSizeeleData.length !== this.sizeeleData.length) {
							newSizeeleData.forEach(element => {
							  element.style = "";
							});
							this.drawLine(newSizeeleData);
						  }
					
						  if (newSingeleData.length !== this.singeleData.length) {

							//this.singeleData = newSingeleData;
							newSingeleData.forEach(element => {
							  element.style = "";
							});
							this.drawLine(newSingeleData);
						  }
					   });
					});
				
					const config = {
					  childList: true, // 监控子节点的添加或删除
					  subtree: true, // 监控所有后代节点
					};
				
					// 监控目标节点
					const targetNode = document.querySelector('#renderview'); // 或者其他容器节点
					observer.observe(targetNode, config);
					
				  });

				  
			},

			drawLine(eleDots) {
			  let styleList = [];
			  for (let index = eleDots.length - 1; index > 0; index--) {
			    const ele = eleDots[index];
			    const eleNext = eleDots[index - 1];
			   
			    if (!eleNext) {
			      continue;
			    }
			    // 记录坐标
				const boundThis = ele.getBoundingClientRect();
				// 下一个点的坐标
				const boundNext = eleNext.getBoundingClientRect();
				// 计算长度和旋转角度
				const x1 = boundThis.x,
					y1 = boundThis.y;
				const x2 = boundNext.x,
					y2 = boundNext.y;
				// 长度
				//let distance = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
				let distance = Math.sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1),);
				if (distance > 140) {
				    distance = distance - 8;
				} else if (distance > 100 && distance <= 140) {
				    distance = distance - 6;
				} else if (distance > 70 && distance <= 100) {
				    distance = distance - 4;
				}else if (distance > 50 && distance <= 70) {
				    distance = distance - 3;
				} else if (distance > 30 && distance <= 50) {
				    distance = distance - 2;
				} else {
				    distance = distance - 1;
				}
				
				// 弧度
				//let radius = Math.atan2(x2 - x1, y2 - y1) * (180 / Math.PI); //单位是deg
				// 弧度
				let radius = Math.atan2(y2 - y1, x2 - x1);
				if (radius < -0.7 && radius > -0.3) {
				  radius -= 0.2;
				} else if (radius < -2.9 && radius > -2.5) {
				  radius -= 0.1;
				}
				//console.log('ele',ele);
				const id = ele.id;
				//计算 left
				let left = radius < Math.PI / 2 ? "10%" : "90%";
				if (id.endsWith('_1') || id.endsWith('_2')) {
					left = '90%';
				} else if (id.endsWith('_8')) {
					left = '10%';
				}
				ele.style.width = `${distance}px`;
				ele.style.transform = `rotate(${radius}rad)`;
				ele.style.left = `${left}`;
			  }
			},
		}
	}
</script>


<style lang="scss">
$color: #F9E29C;
.numActive,.sizeActive,.sdActive{
	width: 32rpx;
	height: 32rpx;
	border-radius: 32upx;
	color: #fff;
	background-color: #ff9900;
	
}
.lableNum {
    // background-color: #ed6e6e;
    // color: #fff;
    position: relative;
	//line-height: 32rpx;
    .line, .sizeline, .singline{
        position: absolute;
        box-sizing: border-box;
        height: 2px;
        background-color: #ff9900;
        transform-origin: left center;
        top: 52%;
		margin-left:5rpx ;
        margin-top: 5rpx;
        -ms-pointer-events: none;
        pointer-events: none;
        z-index: 2;
    }
	.sizeline{
		 background-color: #4aa7ee;
	}
	.singline{
		 background-color: #559F49;
	}
}
.canvas-top1 {
  position: absolute;
  z-index: 9999;
  top:0;
  left:0;
  height:100%;
}
.sizeActive{
	background-color: #519DEE;
}
.sdActive{
	background-color: #559F49;
}
.content {
	border-radius: 10upx;
	display: flex;
	flex: 1;
	flex-direction: column;
}

.flex-box {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
	&:last-child {
		border-bottom: 1upx solid $color;
	}
}

.flex-box > .item {
	flex: 1;
}

.title {
	margin: 20upx 0;
	// color: red;
}
.trendCanvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
}
.genaral-area {
	font-size: 20rpx;
	.item {
		// font-size: 26upx;
		height: 36upx !important;
		line-height: 36upx;
		//color: #00557f;
		//background-color: #3d5e6e;
		//border: 1px solid $color;
		border-width: 1px 1px 0 0;
		padding: 4upx;
		box-sizing: border-box;
		text-align: center;
		&:first-child {
			border-left-width: 1upx;
		}
	}

	.thead {
		.item {
			height: 40rpx !important;
			line-height: 40rpx;
			color: #ffffff;
			font-size:8px;
			box-sizing: border-box;
			//background-color: #4e86a1 !important;
		}
	}

	.table {
		&:last-child {
			border-bottom: 1upx solid $color;
		}
	}
}
// 添加的
.theadSticky {
	position: sticky;
	top: 0px;
	z-index: 20;
}
.theadStickyleft {
	width: 140upx !important;
	position: sticky;
	left: 0px;
	background-color: #7E91FF;
}


</style>
