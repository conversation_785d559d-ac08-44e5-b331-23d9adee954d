<template>
  <u-toast ref="uToast"></u-toast>

</template>

<script>
export default {
	data() {
	    return {
	        show: true,
	    }
	},
	methods: {
		Show(msg){
			this.$refs.uToast.show({
			  type: 'default',
			  message: msg,
			  iconUrl: '@/static/images/icon/default.png'
			});
		},
		Success(msg) {
		  this.$refs.uToast.show({
			type: 'success',
			message: msg,
			duration: 1000,
			iconUrl: '@/static/images/icon/success.png'
		  });
		},
		Error(msg) {
		  this.$refs.uToast.show({
			type: 'error',
			message: msg,
			duration: 1000,
			iconUrl: '@/static/images/icon/error.png'
		  });
		},
		Loading(msg) {
		  this.$refs.uToast.show({
			type: 'loading',
			message: msg,
			iconUrl: '@/static/images/icon/loading.png'
		  });
		},
		Jumpurl(msg,pageurl) {
		  this.$refs.uToast.show({
			  type: 'default',
			  title: '开始跳转',
			  message: msg,
			  url: pageurl,
			  complete() {
				if (pageurl) {
				  uni.navigateTo({
					url: pageurl
				  });
				}
			  }
			});
		},
		
		// ...你可以在这里定义其他的方法，比如 `showWarning`、`showInfo` 等
	}
};
</script>
