<template>
	<view>
		<view class="navlist">

			<view class="navbox">
				<view class="item" v-for="(item,index) in loadData" :key="index">
					<view @click="onsubmit(item.path)" class="itembox">
						<view class="navicon">
							<image :src="item.iconsrc" mode="aspectFill" :class="'icon'+ index "></image>
						</view>
						<view class="navtitle">
							<text>{{item.title}}</text?>
						</view>
					</view>
				</view>
			</view>

		</view>
	</view>
</template>

<script>
	export default {
		name: "MyUTabbar",
		data() {
			return {

			};
		},
		props: {
			loadData: {
				type: Array,
				required: true
			},
		},
		computed: {
			navItems() {
				return this.loadData.map(item => ({
					...item,
					iconsrc: '@/static/images/icon/' + item.iconsrc,
				}));
			}
		},
		mounted() {

		},
		methods: {
			onsubmit(path) {
				console.log('nav path', path);
				if (path == 'gameShow') {
					this.$emit('goto', true)
				} else {
					this.$emit('goto', path)
				}

			},
		},
	}
</script>

<style lang="scss">
	.navlist {
		width: 100vw;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;

		.navline {
			width: 100vw;
			height: 20rpx;
			background-color: #f29100;
		}

		.navbox {
			width: 100vw;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;

			.item {
				height: 100rpx;
				margin-right: 20rpx;
				margin-left: 20rpx;
				margin-top: 10rpx;

				.itembox {
					display: flex;
					flex-direction: column;
					text-align: center;
				}

				.navicon {
					width: 100rpx;

					.icon0,
					.icon1,
					.icon2 {
						width: 50rpx;
						height: 50rpx;
					}
				}

				.navtitle {
					font-size: 24rpx;
					margin-top: -5rpx;
					color: #f29100;
				}
			}
		}

	}
</style>