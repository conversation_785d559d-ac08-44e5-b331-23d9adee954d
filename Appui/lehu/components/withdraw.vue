<template>
	<view class="warp">
		<u-loading-icon text="稍等正在加载" color="red" textSize="18" :show="loading"></u-loading-icon>
		<my-toast ref="myToast"></my-toast>
		<view class="boxlist" v-if="aShow">
			<view class="tiprow">
				<u--text type="info" prefixIcon="server-man" align='center' iconStyle="font-size: 20px;color:red;"
					text="在线客服" @click="openurl" size="26rpx" color="red"></u--text>
				<u--text type="info" prefixIcon="rmb-circle" align='center' iconStyle="font-size: 20px;color:red;"
					text="提现记录>>" @click="withdrawLogs" size="26rpx" color="red"></u--text>
			</view>
			<view class="item">
				<view class="sub_item"><u--text text="可提现佣金:" type="info" size="24rpx"></u--text>
					<u--text :text="userInfo.commission" size="40rpx" mode="price" type="error" :bold="true"></u--text>
				</view>
				<u-button type="warning" text="佣金提现" throttleTime="2000" :loading="loading1" loadingText="佣金提现中"
					@click="txCommission" style="margin-top:10rpx;"></u-button>
			</view>
			<view class="item">
				<u--form labelPosition="left" :model="txList" :rules="rules" ref="uForm" :errorType="errorType">
					<view class="sub_item" style="border-bottom: 1px solid #eee;"><u--text text="钱包余额:" type="info"
							size="24rpx"></u--text>
						<u--text :text="userInfo.money" size="48rpx" mode="price" type="error" :bold="true"></u--text>
					</view>
					<view class="txjin">
						<view class="t1"><u-text type="info" text="提现金额" size="24rpx" align="left"></u-text></view>
						<view class="t2">
							<u-form-item prop="amount">
								<u--input :placeholder="'提现金额(最低'+ tx_min +'起)'" border="none" v-model="txList.amount"
									fontSize="26" inputAlign="center" style="height: 35rpx;"></u--input>
							</u-form-item>
						</view>
					</view>
					<view class="txjin">
						<view class="t1"><u--text text="提现账户" type="info" size="24rpx"></u--text></view>
						<view class="t2">
							<u-form-item prop="index">
								<picker @change="change" :value="txList.index"
									:range="availableChannels.map(item => item.info)" range-key="0">
									<view class="uni-input">
										<view v-if="txList.index == null ">请选择提现账户卡号</view>
										<view v-else><u--text :text="availableChannels[txList.index].info" size="24rpx"
												:lines="1" type="info"></u--text></view>
										<view><uni-icons type="bottom" size="10" color="red"></uni-icons></view>
									</view>
								</picker>
							</u-form-item>
						</view>
					</view>
					<view class="padding-top-10 margin-top-10" style="height:60rpx;">
						<u--text text="查看/绑定提现账户,请移步到主页->[设置]" type="error" align="center" size="20rpx"></u--text>
					</view>
					<view class="password">
						<u-modal :show="show" title="请输入支付密码" showCancelButton @confirm="confirm" @cancel="cancel"
							:zoom="true" width="600rpx">
							<u-form-item prop="zfpassword">
								<u--input placeholder="" type="password" border="surround" inputAlign="center"
									v-model="txList.zfpassword" fontSize="26"
									style="height: 35rpx;width:360rpx;"></u--input>
							</u-form-item>
						</u-modal>
					</view>
					<u-button type="warning" :disabled="disabled" text="余额提现" throttleTime="2000" :loading="loading2"
						loadingText="余额提现中" @click="openmodel"></u-button>
				</u--form>
			</view>
		</view>

		<!-- 提现记录 -->
		<view class="logs" v-if="logshow">
			<view class="tiprow">
				<u--text type="info" iconStyle="font-size: 20px;color:red;" text="显示最新的20条提现记录" size="24rpx"
					color="red"></u--text>
			</view>
			<view class="boxlist">
				<u-list height="550">
					<u-list-item v-for="(ritem, index) in withdrawList" :key="index">
						<view class="boxitem" v-if="withdrawList">
							<u--text type="info" iconStyle="font-size: 20px;color:red;" :text="ritem.created_at"
								size="24rpx" color="red"></u--text>
							<u--text type="error" mode="price" :text="ritem.amount" size="24rpx" align="center"
								color="red"></u--text>
							<!-- 当状态为 0 时显示 -->
							<view v-if="ritem.status == 0">
								<u-tag text="待审" size="mini" type='warning' shape="circle"></u-tag>
							</view>
							<!-- 当状态为 1 时显示 -->
							<u-tag v-else-if="ritem.status == 1" text="通过" size="mini" type='success'
								shape="circle"></u-tag>
							<!-- 当状态为 2 时显示 -->
							<u-tag v-else-if="ritem.status == 2" text="拒绝" size="mini" type='warning'
								shape="circle"></u-tag>
						</view>
						<view v-else>
							<u--text type="info" iconStyle="font-size: 20px;color:red;" text="暂无提现记录" size="24rpx"
								color="red"></u--text>
						</view>
						</u-list-item?>
				</u-list>
			</view>

		</view>

	</view>
</template>

<script>
	import MyToast from '@/components/MyToast.vue';
	export default {
		name: "withdraw",
		components: {
			MyToast
		},
		data() {
			return {
				disabled: false,
				loading: false,
				loading1: false,
				loading2: false,
				kefuurl: '',
				show: false,
				txShow: false,
				logshow: false,
				aShow: true,
				tx_min: 0,
				withdrawList: [],
				errorType: "toast",
				title: '未查到绑定信息，请检查',
				value: 0,
				availableChannels: [],
				txList: {
					amount: '',
					index: null,
					mark: 0,
					zfpassword: '',
				},
				userInfo: {
					uid: '',
					money: 0,
					username: '',
					commission: 0,

				},
				rules: {
					amount: [{
							type: 'string',
							required: true,
							message: '请输入提现金额',
							trigger: ['blur', 'change']
						},
						{
							validator: (rule, value, callback) => {
								return uni.$u.test.amount(value);
							},
							message: '金额填写不正确',
							trigger: ['blur', 'change'],
						},
						{
							validator: (rule, value, callback) => {
								if (value && parseFloat(value) > this.userInfo.money) {
									callback(new Error('提现金额不能大于余额'));
								} else if (value && parseFloat(value) < this.tx_min) {
									callback(new Error('最低提现金额' + this.tx_min));
								} else {
									callback();
								}
							},
							trigger: ['blur', 'change'],
						},
					],
					index: [{
						validator: (rule, value, callback) => {
							console.log('value', value);
							if (value === null || value === undefined) {
								callback(new Error('请选择提现账户'));
							} else {
								callback();
							}
						},
						trigger: ['blur', 'change'],
					}, ],
					zfpassword: [{
						type: 'string',
						required: true,
						message: '请输入支付密码',
						trigger: ['blur', 'change']
					}],

				}
			};
		},
		mounted() {
			this.myWallet();
			this.mycfg();
		},
		methods: {

			change(e) {
				const selectedIndex = e.target.value;
				this.txList.index = selectedIndex;
				const selectedChannel = this.availableChannels[selectedIndex];
				if (selectedChannel) {
					this.txList.mark = selectedChannel.id; // 这里的 id 是 1, 2, 或 3
				}
			},

			// 获取用户钱包信息
			myWallet() {
				uni.$u.http.post('/api/user/userinfo').then(res => {
					if (res.code !== 200) {
						this.$refs.myToast.Error(res.msg);
						return false;
					}
					this.userInfo.uid = res.data.uid;
					this.userInfo.username = res.data.username;
					this.userInfo.money = res.data.money;
					this.userInfo.commission = res.data.commission;
					if (res.data.is_bank_bind == 0 && res.data.is_alipay_bind == 0 && res.data.is_usdt_bind == 0) {
						this.title = '提现账户未绑定';
					} else {
						if (res.data.is_bank_bind !== 0) {
							let bank_info = res.data.bank_realname + "|" + res.data.bank_account;
							this.availableChannels.push({
								id: 1,
								info: bank_info
							});
						}
						if (res.data.is_alipay_bind !== 0) {
							let alipay_info = res.data.alipay_realname + "|" + res.data.alipay_account;
							this.availableChannels.push({
								id: 2,
								info: alipay_info
							});
						}
						if (res.data.is_usdt_bind !== 0) {
							let usdt_info = res.data.usdt_account;
							this.availableChannels.push({
								id: 3,
								info: usdt_info
							});
						}
						if (res.data.is_huya_bind !== 0) {
							let huya_info = '【虎牙】' + res.data.huya_id + "|" + res.data.huya_phone;
							this.availableChannels.push({
								id: 5,
								info: huya_info
							});
						}
						if (res.data.is_inke_bind !== 0) {
							let inke_info = '【映客】' + res.data.inke_id + "|" + res.data.inke_phone;
							this.availableChannels.push({
								id: 6,
								info: inke_info
							});
						}
					}

					console.log('res: ', res);
				}).catch((err) => {
					console.log('报错返回', err);
					this.$refs.myToast.Error(err);
				});

			},
			//佣金提现
			txCommission() {
				this.loading1 = true;
				if (this.userInfo.commission == 0) {
					this.$refs.myToast.Error('大哥，佣金不足啊');
					setTimeout(() => {
						this.loading1 = false;
					}, 1000)
					return false;
				}
				uni.$u.http.post('/api/wallet/txOfcommission').then(res => {
					if (res.code !== 200) {
						this.$refs.myToast.Error(res.msg);
						return false;
					}
					this.$refs.myToast.Success(res.msg);
					this.loading1 = false;
					this.myWallet();
				}).catch((err) => {
					this.loading1 = false;
					console.log('报错返回', err);
					this.$refs.myToast.Error(err);
				});
			},
			openmodel() {
				this.$refs.uForm.validate().then(rrr => {
					//弹出密码框
					this.show = true;
				}).catch(errors => {
					console.log('校验不通过');
				})

			},
			confirm() {
				this.$refs.uForm.validate().then(rrr => {
					//弹出密码框
					this.show = false;
					this.txBalance();
				}).catch(errors => {
					console.log('校验不通过');
				})
			},
			cancel() {
				console.log('cancel');
				this.show = false;
			},

			//余额提现
			txBalance() {

				let txdata = {
					channel: this.txList.mark,
					amount: this.txList.amount,
					zfpassword: this.txList.zfpassword,
				};
				uni.$u.http.post('/api/wallet/txOfbalance', txdata).then(res => {
					if (res.code !== 200) {
						this.$refs.myToast.Error(res.msg);
						return false;
					}
					this.$refs.myToast.Success(res.msg);
					this.txList.zfpassword = '';
					this.txList.amount = '';
					this.txList.index = null;
					console.log('res: ', res);
					this.myWallet();
				}).catch((err) => {
					console.log('报错返回', err);
					this.$refs.myToast.Error(err);
					this.txList.zfpassword = '';
					this.txList.amount = '';
				});
			},

			mycfg() {
				uni.$u.http.post('/api/config/getconfig').then(res => {
					if (res.code !== 200) {
						this.$refs.myToast.Error(res.msg);
						return false;
					}
					console.log('res my cfg: ', res);
					this.kefuurl = res.data.kefuurl;
					this.tx_min = res.data.tx_min;
				}).catch((err) => {
					console.log('获取配置文件报错返回', err);
					//this.$refs.myToast.Error(err);
				});
			},
			openurl() {
				if (this.kefuurl) {
					//const randomID = Math.floor(Math.random() * (999999 - 100000 + 1)) + 100000;
					const id = uni.getStorageSync('userinfo').id;
					const username = uni.getStorageSync('userinfo').username;
					let url = this.kefuurl;
					const encodedId = encodeURIComponent(id);
					const encodedUsername = encodeURIComponent(username);
					url = url.replace('visiter_id=test', `visiter_id=${encodedId}`);
					url = url.replace('visiter_name=test', `visiter_name=${encodedUsername}`);
					if (process.env.UNI_PLATFORM === 'h5') {
						window.open(url);
					} else {
						// plus.runtime.openURL(url, function(res) {
						//    this.$refs.myToast.Error('客服链接反馈'+ res);
						// });
						plus.runtime.openWeb(url);
					}
				}
			},
			withdrawLogs() {
				console.log('withdrawLogs');
				this.logshow = true;
				this.loading = true;
				this.aShow = false;
				uni.$u.http.post('/api/wallet/getWithdrawInfo').then(res => {
					if (res.code !== 200) {
						this.$refs.myToast.Error(res.msg);
						return false;
					}
					this.loading = false;
					this.withdrawList = res.data;
					console.log('withdrawList: ', res);
				}).catch((err) => {
					console.log('报错返回', err);
					this.loading = false;
					this.$refs.myToast.Error(err);

				});
			},
		},
	}
</script>

<style lang="scss">
	.uni-picker-container {
		z-index: 10199;
	}

	.warp {
		width: 80vw;
		display: flex;
		justify-content: center;
		flex-direction: column;
		align-items: center;

		.boxlist {
			width: 80vw;
			margin-top: -20rpx;

			.tiprow {
				display: flex;
				flex-direction: row;
				width: 80vw;
				justify-content: center;
				align-items: center;
			}

			.item {
				padding: 20rpx 20rpx;
			}

			.sub_item {
				display: flex;
				flex-direction: row;
				align-items: center;
			}

			.txjin {
				display: flex;
				flex-direction: row;
				align-items: center;
				border-bottom: 1px solid #eaeaea;
				height: 80rpx;
				line-height: 80rpx;

				.t1 {
					flex: 1;
				}

				/* u--input 的 flex 值 */
				.t2 {
					flex: 3;
					/* 这个值可以根据你的需求进行调整 */
					text-align: center;

					.uni-input {
						width: 370rpx;
						display: flex;
						justify-content: space-between;
						align-items: center;
						flex-direction: row;
						font-size: 24rpx;
						flex-wrap: nowrap;
					}
				}
			}

			.boxitem {
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;
				padding: 5rpx 10rpx;
				border-bottom: 1px solid #e5e9f2;
			}

			.txbox {
				margin-top: 10rpx;
				margin-bottom: 10rpx;

				.txlist {
					display: flex;
					flex-direction: row;
					align-items: center;

					.addinfo_right {
						border: 1px solid #dadada;
						font-size: 26rpx;
					}
				}
			}

			.password {
				width: 70vw;
			}

			.error {
				margin-top: 10rpx;
				border: 1px solid #eee;
				padding: 10rpx 10rpx;
				height: 40rpx;
				line-height: 40rpx;
				text-align: center;
				color: #000;
				margin-bottom: 10rpx;
			}

		}

		.logs {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;

			.tiprow {
				height: 50rpx;
				line-height: 50rpx;
				padding-bottom: 10rpx;
				margin-bottom: 10rpx;
			}
		}

	}
</style>