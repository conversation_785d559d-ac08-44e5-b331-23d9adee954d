<template>
	<view class="warp">
		<my-toast ref="myToast"></my-toast>
		<view class="nuter" v-if="nutershow">

			<view :class="target==1?'active':''" @click="setIndex" data-index="1">常用通道</view>
		</view>
		<view class="warp-main" v-if="mainshow">
			<swiper :duration="500" :current="thisindex" :data-index='thisindex' @change="toggle" :disable-touch="true"
				circular>
				<swiper-item>
					<!-- 对应推荐通道 -->
					<view class="action">
						<view class="tiprow">
							<u--text type="info" prefixIcon="server-man" align='center'
								iconStyle="font-size: 20px;color:red;" text="在线客服" @click="openurl" size="26rpx"
								color="red"></u--text>
							<u--text type="info" prefixIcon="rmb-circle" align='center'
								iconStyle="font-size: 20px;color:red;" text="充值记录>>" @click="recordLogs" size="26rpx"
								color="red"></u--text>
						</view>
						<u-loading-icon text="稍等正在加载" color="red" textSize="18" :show="loading"></u-loading-icon>
						<view class="tiprow" style="margin-top:30rpx;">
							<view v-if="rechargeList == 0">
								<u--text type="error" iconStyle="font-size: 20px;color:red;" text="暂无充值通道推荐,请切换常用通道"
									size="24rpx" align="center" color="red"></u--text>
							</view>
							<view v-else>
								<u--text type="error" iconStyle="font-size: 20px;color:red;" text="请点击充值提交后,联系客服索取充值码"
									size="24rpx" align="center" color="red"></u--text>
							</view>
						</view>
					</view>

					<view class="boxlist" v-if="isRechageView">
						<u-list height="300">
							<u-list-item v-for="(ritem, index) in rechargeList" :key="index">
								<view class="boxitem">
									<u--text type="info" iconStyle="font-size: 20px;color:red;"
										:text="'推荐通道ID：'+ ritem.id" size="24rpx" color="red"></u--text>
									<u--text type="error" mode="price" :text="ritem.amount" size="24rpx" align="left"
										color="red"></u--text>
									<u-button type="error" text="充值" throttleTime="2000" :loading="loading" size="mini"
										loadingText="加载中" class="rrbtn" shape="circle" plain="false"
										@click="submitRecharge(ritem.id,ritem.amount)"></u-button>
								</view>
							</u-list-item>
						</u-list>
					</view>
					<view class="boxlist" v-if="isRechageShow">
						<view class="sbox hx1">
							<view><u--text text="充值金額:" size="24rpx" align="center" color="#000000"></u--text>
							</view>
							<view><u--text type="error" :text="txlist['amount']" size="24rpx" align="center"
									color="red"></u--text>
							</view>
						</view>
						<view class="sbox hx1">
							<view><u--text text="充值渠道:" size="24rpx" align="center" color="#000000"></u--text>
							</view>
							<view>
								<u--text type="error" :text="channelText" size="24rpx" align="center"></u--text>
							</view>
						</view>
						<view class="sbox hx1" v-if="txlist['channel'] == 1">
							<view><u--text text="开户银行:" size="24rpx" align="center" color="#000000"></u--text>
							</view>
							<view><u--text type="error" :text="txlist['tx_bank_type']" size="24rpx" align="center"
									color="red"></u--text>
							</view>
						</view>
						<view class="sbox hx1">
							<view><u--text text="充值姓名:" size="24rpx" align="center" color="#000000"></u--text>
							</view>
							<view><u--text type="error" :text="txlist['tx_bank_realname']" size="24rpx" align="center"
									color="red"></u--text>
							</view>
						</view>
						<view class="sbox hx1">
							<view><u--text text="充值卡号:" size="24rpx" align="center" color="#000000"></u--text>
							</view>
							<view><u--text type="error" :text="txlist['tx_bank_account']" size="24rpx" align="center"
									color="red"></u--text>
							</view>
						</view>
						<view class="sbox margin-top-20">
							<u--text text="用户注意: 充值前请联系客服确认卡号或姓名是否有效,未确认造成的损失,由客户自行负责.本平台概不负责!!!" size="24rpx"
								align="center" color="red"></u--text>
						</view>
						<view class="btn"><u-button type="warning" text="同意并已知晓风险" @click="submitAction"
								throttleTime="2000"></u-button></view>
					</view>


				</swiper-item>

				<swiper-item>
					<!-- 对应常用通道 -->
					<view class="type" v-if="bShow">
						<view class="tiprow">
							<u--text type="info" prefixIcon="server-man" align='center'
								iconStyle="font-size: 20px;color:red;" text="在线客服" @click="openurl" size="26rpx"
								color="red"></u--text>
							<u--text type="info" prefixIcon="rmb-circle" align='center'
								iconStyle="font-size: 20px;color:red;" text="充值记录>>" @click="recordLogs" size="26rpx"
								color="red"></u--text>
						</view>
						<u--form labelPosition="left" :model="rechargeinfo" :rules="rules" ref="uForm"
							:errorType="errorType">
							<view class="box">
								<u-form-item prop="channel">
									<u-radio-group v-model="rechargeinfo.channel" placement="row" @change="groupChange">
										<u-radio :customStyle="{marginBottom: '10px'}" v-for="(item, index) in typelist"
											size="26" :key="index" :label="item.name" :name="item.mark">
											<template #default="{ label }">
												<view class="radio-content" @change="groupChange">
													<view class="image-with-label">
														<image :src="item.imgurl" mode="aspectFit" class="type_img">
														</image>
														<text class="image-label">{{ item.name }}</text> <!-- 添加文字标识 -->
													</view>
												</view>
											</template>

										</u-radio>
									</u-radio-group>
								</u-form-item>
							</view>
							<view class="boxlist">
								<view class="tip"><u--text type="info" text="请选择充值的渠道 欢迎咨询在线客服" size="24rpx"></u--text>
								</view>
								<view class="sbox">
									<u-form-item prop="amount">
										<view class="txt"><u--text type="info" text="充值金额" align="right"
												size="26"></u--text>
										</view>
										<view><u--input placeholder="请输入充值金额" name="amount" border="surround"
												class="sinput" v-model="rechargeinfo.amount"></u--input></view>
									</u-form-item>
								</view>
								<view class="sbox">
									<u-form-item prop="notetxt">
										<view class="txt"><u--text type="info" text="姓名或ID" align="right"
												size="26"></u--text>
										</view>
										<view><u--input placeholder="请输入ID或姓名" name="notetxt" border="surround"
												class="sinput" v-model="rechargeinfo.notetxt"></u--input></view>
									</u-form-item>
								</view>
								<view class="tip"><u--text type="error" :text="'(注意:充值金额最低'+ cz_min +'起 必须保留转账小数点)'"
										size="24rpx"></u--text>
								</view>
								<view class="btn"><u-button type="warning" text="确认充值" throttleTime="1000"
										:loading="loading" loadingText="加载中" @click="submitForm"></u-button></view>

							</view>
						</u--form>
					</view>

					<view class="action" v-if="aShow">
						<view class="tiprow">
							<u--text type="info" prefixIcon="server-man" iconStyle="font-size: 20px;color:red;"
								text="在线客服" @click="openurl" size="24rpx" color="red"></u--text>
							<u--text type="info" prefixIcon="rmb-circle" iconStyle="font-size: 20px;color:red;"
								text="充值记录>>" @click="recordLogs" size="24rpx" color="red"></u--text>
						</view>
						<view style="margin-top:10rpx;"><u-text type="error" :text="'你的充值订单号: ' + order_sn"
								size="24rpx"></u-text>
						</view>
						<view class="action-box" v-if="rechargeinfo.channel == '1'">
							<u-row customStyle="margin-bottom: 10px">
								<u-col span="4" style="margin-right:10rpx;">
									<view class="demo-layout bg-purple-light"><u-text align="center" text="转账金额"
											size="24"></u-text>
									</view>
								</u-col>
								<u-col span="8">
									<view class="demo-layout bg-purple"><u-tooltip :text="'¥' + rechargeinfo.newamount"
											bgColor="#e3e4e6" :copy-text="rechargeinfo.newamount"
											color="#ff9900"></u-tooltip>
									</view>
								</u-col>
							</u-row>
							<u-row customStyle="margin-bottom: 10px">
								<u-col span="4" style="margin-right:10rpx;">
									<view class="demo-layout bg-purple-light"><u-text align="center" text="收款姓名"
											size="24"></u-text>
									</view>
								</u-col>
								<u-col span="8">
									<view class="demo-layout bg-purple"><u-tooltip :text="payList.bank_pay_name"
											bgColor="#e3e4e6" :copy-text="payList.bank_pay_name"
											color="#ff9900"></u-tooltip></view>
								</u-col>
							</u-row>
							<u-row customStyle="margin-bottom: 10px">
								<u-col span="4" style="margin-right:10rpx;">
									<view class="demo-layout bg-purple-light"><u-text align="center" text="银行卡号"
											size="24"></u-text>
									</view>
								</u-col>
								<u-col span="8">
									<view class="demo-layout bg-purple"><u-tooltip :text="payList.bank_pay_account"
											bgColor="#e3e4e6" :copy-text="payList.bank_pay_account"
											color="#ff9900"></u-tooltip>
									</view>
								</u-col>
							</u-row>
							<u-row customStyle="margin-bottom: 10px">
								<u-col span="4" style="margin-right:10rpx;">
									<view class="demo-layout bg-purple-light"><u-text align="center" text="收款银行"
											size="24"></u-text>
									</view>
								</u-col>
								<u-col span="8">
									<view class="demo-layout bg-purple"><u-tooltip :text="payList.bank_pay_type"
											bgColor="#e3e4e6" :copy-text="payList.bank_pay_type"
											color="#ff9900"></u-tooltip></view>
								</u-col>
							</u-row>
						</view>

						<view class="action-box" v-if="rechargeinfo.channel == '2'">
							<view class="margin-top-20">
								<u-text align="center" text="如需充值码请联系在线客服" size="24rpx"></u-text>
							</view>
							<view style="margin-top:10rpx;margin-bottom: 10rpx;">
								<u-text align="center" :text="'当前转账金额 ¥' + rechargeinfo.newamount" size="28rpx"
									color="red"></u-text>
							</view>

							<view class="margin-top-20">
								<u-row customStyle="margin-bottom: 10px">
									<u-col span="4" style="margin-right:10rpx;">
										<view class="demo-layout bg-purple-light"><u-text align="center" text="转账金额"
												size="24"></u-text>
										</view>
									</u-col>
									<u-col span="8">
										<view class="demo-layout bg-purple"><u-tooltip
												:text="'¥' + rechargeinfo.newamount" bgColor="#e3e4e6"
												:copy-text="rechargeinfo.newamount" color="#ff9900"></u-tooltip>
										</view>
									</u-col>
								</u-row>
								<u-row customStyle="margin-bottom: 10px">
									<u-col span="4" style="margin-right:10rpx;">
										<view class="demo-layout bg-purple-light"><u-text align="center" text="支付宝(1)"
												size="24"></u-text>
										</view>
									</u-col>
									<u-col span="8">
										<view class="demo-layout bg-purple"><u-tooltip
												:text="payList.alipay_pay_name_1+ '|'+ payList.alipay_pay_account_1"
												bgColor="#e3e4e6"
												:copy-text="payList.alipay_pay_name_1+ '|'+ payList.alipay_pay_account_1"
												color="#ff9900"></u-tooltip></view>
									</u-col>
								</u-row>
								<u-row customStyle="margin-bottom: 10px">
									<u-col span="4" style="margin-right:10rpx;">
										<view class="demo-layout bg-purple-light"><u-text align="center" text="支付宝(2)"
												size="24"></u-text>
										</view>
									</u-col>
									<u-col span="8">
										<view class="demo-layout bg-purple"><u-tooltip
												:text="payList.alipay_pay_name_2+ '|'+ payList.alipay_pay_account_2"
												bgColor="#e3e4e6"
												:copy-text="payList.alipay_pay_name_2+ '|'+ payList.alipay_pay_account_2"
												color="#ff9900"></u-tooltip>
										</view>
									</u-col>
								</u-row>

							</view>
							<!-- <view>
								<image :src="payimgobj.url" mode="aspectFill" class="payimg"></image>
							</view> -->
							<!-- <view><u-text :text="'支付宝名字：' + payimgobj.name" size="24rpx" color="red"></u-text></view>
							<view class="margin-top-10"><u-button type="primary" :disabled="disabled" :text="refreshtxt"
									throttleTime="2000" @click="refreshQrcode" size="mini" shape="circle"></u-button>
							</view> -->

						</view>

						<view class="action-box" v-if="rechargeinfo.channel == '3'">
							<view style="margin-top:10rpx;margin-bottom: 10rpx;">
								<u-text align="center" :text="'当前转账金额 ¥' + rechargeinfo.newamount" size="28rpx"
									color="red"></u-text>
								<u-text align="center" text="请联系在线客服取充值码进行充值" size="24rpx"></u-text>
							</view>
							<!-- <view>
								<image :src="payimgobj.url" mode="aspectFill" class="payimg"></image>
							</view> -->
							<!-- <view><u-text :text="'微信名字：' + payimgobj.name" size="24rpx" color="red"></u-text></view>
							<view class="margin-top-10"><u-button type="primary" :disabled="disabled" :text="refreshtxt"
									throttleTime="2000" @click="refreshQrcode" size="mini" shape="circle"></u-button>
							</view> -->
						</view>

						<view class="action-box" v-if="rechargeinfo.channel == '4'">
							<view style="margin-top:10rpx;margin-bottom: 10rpx;">
								<u-text align="center"
									:text="'转账金额 ¥' + rechargeinfo.newamount + ' USDT汇率: ' + usdt_rate"
									size="24rpx"></u-text>
								<u-text align="center" :text="'当前需转账: '+ requiredUsdt + ' USDT (TRC20网络)' " size="24rpx"
									color="red"></u-text>
							</view>
							<view class="uqrcode">
								<view class="qrcode">
									<uqrcode ref="uqrcode" canvas-id="qrcode" :value="payList.usdt_pay_account" size=130
										:options="{ margin: 10, }"></uqrcode>
								</view>
							</view>
							<view><u-text :text="'转账地址：' + payList.usdt_pay_account" size="24rpx" color="red"
									align="center"></u-text></view>
							<view class="margin-top-10"><u-text :text="'请用加密钱包扫描二维码'" size="24rpx"
									color="info"></u-text></view>
							<view class="margin-top-10"><u-button type="primary" size="small" text="复制转账地址"
									@click="copy(payList.usdt_pay_account)"></u-button></view>
						</view>

						<view class="note">
							<view>
								<view><u--text type="info" text="1.避免支付风控，转账金额小位数不能忽略，否则造成损失本平台概不负责"></u--text></view>
								<view><u--text type="info" text="2.USDT充值默认TRC-20通道，认真核对转错概不负责,汇率换算请联系客服"></u--text>
								</view>
								<view><u--text type="info" text="3.转账成功后，请把转账截图提供在线客服"></u--text></view>
							</view>
						</view>

					</view>
				</swiper-item>
				<view class="btn"><u-button type="warning" text="已知晓 确认转账" @click="submitAction"
						throttleTime="2000"></u-button></view>
			</swiper>

		</view>



		<!-- 充值记录 -->
		<view class="logs" v-if="logshow">
			<view class="tiprow">
				<u--text type="info" iconStyle="font-size: 20px;color:red;" text="显示最新的20条充值记录" size="24rpx" color="red"
					align="center"></u--text>
			</view>
			<view class="boxlist">
				<u-list height="550">
					<u-list-item v-for="(ritem, index) in recordList" :key="index">
						<view class="boxitem" v-if="recordList">
							<u--text type="info" iconStyle="font-size: 20px;color:red;" :text="ritem.created_at"
								size="24rpx" color="red"></u--text>
							<u--text type="error" mode="price" :text="ritem.amount" size="24rpx" align="center"
								color="red"></u--text>
							<!-- 当状态为 0 时显示 -->
							<view v-if="ritem.status == 0">
								<u-tag text="待审" size="mini" type='warning' shape="circle"></u-tag>
								<!-- <u-tag text="取消" size="mini"  shape="circle" @click="cancelRecharge(ritem.order_id)"></u-tag> -->
							</view>
							<!-- 当状态为 1 时显示 -->
							<u-tag v-else-if="ritem.status == 1" text="通过" size="mini" type='success'
								shape="circle"></u-tag>
							<!-- 当状态为 2 时显示 -->
							<u-tag v-else-if="ritem.status == 2" text="拒绝" size="mini" type='warning'
								shape="circle"></u-tag>
							<!-- 当状态为 3 时显示 -->
							<!-- <u-tag v-else-if="ritem.status == 3" text="已销" size="mini" type='warning'
								shape="circle"></u-tag> -->
						</view>
						<view v-else>
							<u--text type="info" iconStyle="font-size: 20px;color:red;" text="暂无充值记录" size="24rpx"
								color="red"></u--text>
						</view>
					</u-list-item>
				</u-list>
			</view>
		</view>


	</view>
</template>

<script>
	import MyToast from '@/components/MyToast.vue';
	export default {
		name: "recharge",
		components: {
			MyToast
		},
		data() {
			return {
				nutershow: true, //选项显示
				mainshow: true, //充值界面显示
				aShow: false, //动作
				bShow: true, // 充值
				cz_min: 0, //最小充值
				logshow: false,
				loading: false,
				disabled: false,
				isRechageShow: false,
				isRechageView: true,
				text: 'uQRCode',
				link: 'https://www.baidu.com',
				refreshtxt: '刷新二维码',
				countdownTimer: null,
				kefuurl: '',
				typelist: [{
						name: '银行卡',
						disabled: false,
						mark: '1',
						imgurl: '/static/images/icon/bankpay.png'
					},
					{
						name: '支付宝',
						disabled: false,
						mark: '2',
						imgurl: '/static/images/icon/alipay.png'
					},
					{
						name: '微信支付',
						disabled: false,
						mark: '3',
						imgurl: '/static/images/icon/wxpay.png'
					}, {
						name: 'USDT',
						disabled: false,
						mark: '4',
						imgurl: '/static/images/icon/usdtpay.png'
					}
				],
				// u-radio-group的v-model绑定的值如果设置为某个radio的name，就会被默认选中
				radiovalue: '银行卡',
				rechargeinfo: {
					amount: '',
					newamount: '',
					notetxt: '',
					channel: '', // 与单选框的v-model绑定
				},
				order_sn: '', //返回订单号
				payimgobj: {
					url: '',
					name: '',
				}, //返回充值二维码
				payList: {
					alipay_pay_account_1: "",
					alipay_pay_name_1: "",
					alipay_pay_account_2: "",
					alipay_pay_name_2: "",
					bank_pay_account: "",
					bank_pay_name: "",
					bank_pay_type: "",
					usdt_pay_account: "",
					usdt_pay_type: "",
					wx_pay_account: "",
					wx_pay_name: "",
				},
				rules: {
					channel: {
						type: 'string',
						required: true,
						message: '请选择充值渠道',
						trigger: ['blur', 'change']
					},
					amount: [{
							type: 'number',
							required: true,
							message: '金额必须填写',
							trigger: ['blur', 'change']
						},
						{
							validator: (rule, value, callback) => {
								return uni.$u.test.amount(value);
							},
							message: '金额填写不正确',
							trigger: ['blur', 'change'],
						},
						{
							validator: (rule, value, callback) => {
								console.log('验证金额：', value);
								const numValue = parseFloat(value); // 确保将 value 转换为数字
								if (isNaN(numValue)) {
									callback(new Error('金额填写不正确'));
								} else if (numValue < this.cz_min) {
									callback(new Error('充值最低金额' + this.cz_min + '起'));
								} else {
									callback();
								}
							},
							trigger: ['blur', 'change']
						}
					],
					notetxt: {
						type: 'string',
						required: true,
						message: '请填写ID或姓名',
						trigger: ['blur', 'change']
					},

				},
				errorType: 'toast',
				recordList: [],
				rechargeList: [],
				usdt_rate: 0.00, //usdt汇率
				// 当前样式
				target: 0,
				// 当前item位置
				thisindex: 0,
				txlist: [],

			};
		},
		computed: {
			// 计算需要转账的USDT
			requiredUsdt() {
				if (!this.rechargeinfo.newamount || !this.usdt_rate) {
					return '0.00'; // 如果数据缺失，则返回0
				}

				// 确保数据是浮点数类型
				const amount = parseFloat(this.rechargeinfo.newamount);
				const rate = parseFloat(this.usdt_rate);

				if (isNaN(amount) || isNaN(rate) || rate === 0) {
					return '0.00'; // 如果数据无效或汇率为0，则返回0
				}

				// 计算并保留两位小数
				const result = (amount / rate).toFixed(2);

				return result;
			},
			channelText() {
				switch (this.txlist['channel']) {
					case 1:
						return '银行卡';
					case 2:
						return '支付宝';
					case 3:
						return 'USDT';
					default:
						return '银行卡';
				}
			},
		},
		mounted() {
			this.mycfg();
			this.getRechargeList();
		},
		methods: {

			groupChange(n) {
				console.log('groupChange', n);
			},
			saveQrcode() {
				this.$refs.uqrcode.save({
					success: () => {
						uni.showToast({
							icon: 'success',
							title: '保存成功'
						});
					}
				});
			},
			copy(con) {
				//uni.setClipboardData 内容复制到粘贴板
				uni.setClipboardData({
					data: con, //要被复制的内容
					success: () => {
						//复制成功的回调函数
						uni.showToast({
							//提示
							title: '复制成功'
						});
					}
				});
			},
			submitForm() {
				// 执行自定义验证逻辑
				this.$refs.uForm.validate().then(res => {
					this.mypay();
					//uni.$u.toast('校验通过')
					this.loading = true;
					let randomDecimal = Math.random(); // 生成0到1之间的随机数
					this.rechargeinfo.newamount = parseFloat(this.rechargeinfo.amount) + randomDecimal; // 加上随机小数
					this.rechargeinfo.newamount = this.rechargeinfo.newamount.toFixed(2); // 格式化为两位小数
					if (this.rechargeinfo.channel == 2 || this.rechargeinfo.channel == 3) {
						this.getRechargeImg();
					}

					uni.$u.http.post('/api/wallet/recharge', {
						amount: this.rechargeinfo.newamount,
						note: this.rechargeinfo.notetxt,
						type: this.rechargeinfo.channel,
					}).then(res => {
						console.log(res.code);
						setTimeout(() => {
							this.loading = false;
							this.bShow = false;
							this.aShow = true;
							this.order_sn = res.data;

						}, 2000);

						console.log('res: ', res);
					}).catch((err) => {
						console.log('报错返回', err);
						this.$refs.myToast.Error(err);
						this.loading = false;
					});
				})

				// 其他提交逻辑...
			},

			submitAction() {
				console.log('跳转');
				this.$emit('close'); //触 发事件
			},
			mypay() {
				uni.$u.http.post('/api/config/getpay').then(res => {
					if (res.code !== 200) {
						this.$refs.myToast.Error(res.msg);
						return false;
					}
					this.payList = res.data;
					console.log('res: ', res);
				}).catch((err) => {
					console.log('报错返回', err);
					this.$refs.myToast.Error(err);
				});

			},
			goserver() {
				console.log('kf');
			},
			mycfg() {
				uni.$u.http.post('/api/config/getconfig').then(res => {
					if (res.code !== 200) {
						this.$refs.myToast.Error(res.msg);
						return false;
					}
					console.log('res my cfg: ', res);
					this.kefuurl = res.data.kefuurl;
					this.usdt_rate = res.data.usdt_rate; //汇率
					this.cz_min = res.data.cz_min; //最小充值
				}).catch((err) => {
					console.log('获取配置文件报错返回', err);
					//this.$refs.myToast.Error(err);
				});
			},
			openurl() {
				if (this.kefuurl) {
					//const randomID = Math.floor(Math.random() * (999999 - 100000 + 1)) + 100000;
					const id = uni.getStorageSync('userinfo').id;
					const username = uni.getStorageSync('userinfo').username;
					let url = this.kefuurl;
					const encodedId = encodeURIComponent(id);
					const encodedUsername = encodeURIComponent(username);
					url = url.replace('visiter_id=test', `visiter_id=${encodedId}`);
					url = url.replace('visiter_name=test', `visiter_name=${encodedUsername}`);
					if (process.env.UNI_PLATFORM === 'h5') {
						window.open(url);
					} else {
						// plus.runtime.openURL(url, function(res) {
						//    this.$refs.myToast.Error('客服链接反馈'+ res);
						// });
						plus.runtime.openWeb(url);
					}
				}
			},

			//充值记录
			recordLogs() {
				this.nutershow = false;
				this.mainshow = false;
				this.bShow = false;
				this.aShow = false;
				this.logshow = true;
				this.loading = true;
				uni.$u.http.post('/api/wallet/getRechargeInfo').then(res => {
					if (res.code !== 200) {
						this.$refs.myToast.Error(res.msg);
						return false;
					}
					this.loading = false;
					this.recordList = res.data;
					console.log('recordList: ', res);
				}).catch((err) => {
					console.log('报错返回', err);
					this.loading = false;
					this.$refs.myToast.Error(err);

				});
			},
			getRechargeImg() {
				let category = '';
				if (this.rechargeinfo.channel == 2) {
					category = 2; //支付宝分类
				}
				if (this.rechargeinfo.channel == 3) {
					category = 1; //微信分类
				}

				let postdata = {
					category: category,
					limit: 1,
				}
				uni.$u.http.post('/api/config/getpayimg', postdata).then(res => {
					if (res.code !== 200) {
						this.$refs.myToast.Error(res.msg);
						return false;
					}
					console.log('getRechargeImg: ', res.data);
					this.payimgobj.url = res.data[0].url;
					this.payimgobj.name = res.data[0].name;
				}).catch((err) => {
					console.log('报错返回', err);
					//this.$refs.myToast.Error(err);

				});
			},

			refreshQrcode() {
				//倒计时20秒后才能刷新
				if (this.disabled) {
					return;
				}

				this.disabled = true;
				this.getRechargeImg(); // 假设这是你的刷新二维码的方法

				let countdown = 20; // 倒计时时间
				this.refreshtxt = countdown.toString() + '秒后再次刷新'; // 初始化显示

				this.countdownTimer = setInterval(() => {
					countdown--;
					this.refreshtxt = countdown.toString() + '秒后再次刷新'; // 更新显示

					if (countdown <= 0) {
						clearInterval(this.countdownTimer); // 清除定时器
						this.refreshtxt = '刷新二维码'; // 重置为初始文本
						this.disabled = false; // 重启按钮
					}
				}, 1000); // 每秒执行一次
			},
			//查询提现订单
			getRechargeList() {
				uni.$u.http.get('/api/wallet/getRechargeChannel').then(res => {
					if (res.code !== 200) {
						this.$refs.myToast.Error(res.msg);
						return false;
					}
					if (res.data !== '') {
						this.rechargeList = res.data;
					} else {
						this.rechargeList = 0;
					}

					console.log('rechargeList: ', res.data);
				}).catch((err) => {
					console.log('报错返回', err);
					this.loading = false;
					this.$refs.myToast.Error(err);
				});


			},
			//推荐通道提交充值
			submitRecharge(id, amount) {
				uni.$u.http.post('/api/wallet/recharge', {
					recharge_id: id,
					amount: amount
				}).then(res => {
					console.log(res.code);
					this.loading = true;
					setTimeout(() => {
						this.loading = false;
						this.order_sn = res.data;
						this.txlist = this.rechargeList.find(item => item.id == id);
						this.isRechageView = false;
						this.isRechageShow = true;
					}, 2000);

					console.log('res: ', res);
				}).catch((err) => {
					console.log('报错返回', err);
					this.$refs.myToast.Error(err);
					this.loading = false;
				});

			},
			//常用通道提交充值
			nonalRecharge(id, amount) {
				uni.$u.http.post('/api/wallet/recharge', {
					recharge_id: id,
					amount: amount
				}).then(res => {
					console.log(res.code);
					setTimeout(() => {
						this.loading = false;
						this.bShow = false;
						this.aShow = true;
						this.order_sn = res.data;
					}, 2000);
					this.txlist = this.rechargeList.find(item => item.id == id);
					this.isRechageShow = true;
					console.log('res: ', res);
				}).catch((err) => {
					console.log('报错返回', err);
					this.$refs.myToast.Error(err);
					this.loading = false;
				});

			},


			//取消充值
			cancelRecharge(orderId) {
				uni.$u.http.post('/api/wallet/cancelRecharge', {
					'order_id': orderId
				}).then(res => {
					if (res.code !== 200) {
						this.$refs.myToast.Error(res.msg);
						return false;
					}
					this.$refs.myToast.Success(res.msg);
					this.recordLogs();
				}).catch((err) => {
					console.log('报错返回', err);
					this.loading = false;
					this.$refs.myToast.Error(err);
				});
			},

			gopage(url) {
				console.log('跳转');
				this.$Router.replace(url);
			},
			// 切换触发的事件
			toggle(e) {
				let index = e.detail.current
				this.target = index
				if (e.detail.source) {
					e.detail.source = 'touch';
				}
			},
			// 点击nav控制下面的展示
			setIndex(e) {
				let index = e.currentTarget.dataset.index
				this.thisindex = index
			},
		}
	}
</script>

<style lang="scss">
	.warp {
		width: 86vw;
		display: flex;
		justify-content: center;
		flex-direction: column;
		align-items: center;

		.type {
			width: 86vw;
			height: 250rpx;
			border-radius: 15rpx 15rpx;

			.tiprow {
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;
				text-align: center;
			}

			.box {
				width: 86vw;
				display: flex;
				align-items: center;
				text-align: center;
				justify-content: center;

				.radio-content {
					padding: 5rpx 10rpx;

					.image-with-label {
						display: flex;
						flex-direction: column; // 设置为 column 以垂直排列图像和文本
						align-items: center;

						.type_img {
							width: 50rpx; // 可以根据需要调整图像大小
							height: 50rpx;
						}

						.image-label {
							font-size: 24rpx; // 可以根据需要调整字体大小
							color: #333; // 可以根据需要调整字体颜色
						}
					}
				}
			}
		}

		.boxlist {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			width: 86vw;

			.sbox {
				display: flex;
				flex-direction: row;
				justify-content: space-between; // 确保文本和输入框之间的空间
				align-items: flex-end; // 使输入框对齐到底部

				width: 80%; // 使 sbox 占满整个父元素的宽度

				.txt {
					margin-right: 20rpx;
					line-height: 60rpx;
					text-align: right;
				}

				.sinput {
					height: 40rpx;
					width: 250rpx;
					text-align: right; // 使输入文本向右对齐
				}
			}

			.tip {
				height: 40rpx;
				line-height: 40rpx;
				padding: 10rpx 0;
			}

			.btn {
				height: 100rpx;
				margin-top: 10rpx;
			}

			.boxitem {
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;
				padding: 5rpx 10rpx;
				border-bottom: 1px solid #e5e9f2;
			}

			.rrbtn {
				margin-right: 28rpx;
				width: 50rpx;
				position: relative;
				top: 0rpx;

			}

			.hx1 {
				height: 45rpx;
				line-height: 45rpx;
				border-bottom: 1px solid #dadada;
			}
		}

		.payimg {
			width: 300rpx;
			height: 400rpx;
		}

		.action {
			width: 86vw;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			background-color: #fff;
			padding-bottom: 50rpx;
		}

		.tiprow {
			display: flex;
			flex-direction: row;
			width: 100%;
			justify-content: center;
			align-items: center;

		}

		.action-box {
			width: 75vw;
			margin-top: 30rpx;
			border: 1px solid #eee;
			border-radius: 15rpx 15rpx;
			padding: 10rpx 10rpx;
			display: flex;
			flex-direction: column;
			//align-items: center;
			justify-content: center;

			.uqrcode {
				width: 56vw;

				.qrcode {
					display: flex; // 使用flex布局
					justify-content: center; // 水平居中
					align-items: center; // 垂直居中
				}

				.btn {
					display: flex;
					flex-direction: row;
					justify-content: center;
					align-items: center;
				}
			}

			.bg-purple {
				color: #000000;
				padding: 5rpx 10rpx;
			}

			.bg-purple-light {
				background: #e5e9f2;
				padding: 5rpx 10rpx;
			}

			.bg-purple-dark {
				background: #99a9bf;
			}
		}

		.note {
			padding: 5rpx 5rpx;
			width: 86vw;
			margin: 10rpx auto;
			margin-left: 20rpx;
			margin-right: 20rpx;
			display: flex;
			align-items: center;
			justify-content: center;

		}

		.btn {
			width: 350rpx;
			margin-top: 20rpx;
		}

		.logs {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;

			.tiprow {
				height: 50rpx;
				line-height: 50rpx;
			}
		}

		.warp-main {
			width: 96%;
			background-color: #ffffff;
			height: 560rpx;

			uni-button {
				background: linear-gradient(45deg, #ff2c55, #FF4155);
				color: #fff;
				border-radius: 20rpx;
				position: relative;
				top: 20rpx;
			}
		}

		.uni-picker-container .uni-picker-custom {
			border-radius: 20px;
		}

		.nuter {
			width: 100%;
			height: 80rpx;
			background-color: #FFC055;
			line-height: 60rpx;
			display: flex;
			justify-content: space-around;
			font-size: 24rpx;
			color: #fff;
			position: relative;
			top: -20rpx;

		}

		.nuter view {
			flex: 1;
			font-size: 30rpx;
			text-align: center;
			margin-top: 20rpx;
			transition: all 0.1s ease .1s;
		}

		.active {
			box-sizing: border-box;
			color: #e39a70;
			margin-top: 20rpx;
			background-color: #ffffff;
			border-top-left-radius: 20rpx;
			border-top-right-radius: 20rpx;
			margin-left: 20rpx;
			/* 添加左外边距 */
			margin-right: 20rpx;
			/* 添加右外边距 */
		}

		uni-swiper {
			height: 620rpx;
		}

		.uni-input-placeholder {
			font-size: 26rpx;
			color: #d3d3d3;
		}

		swiper-item {
			width: 100%;
			overflow: hidden;
			/* background-color: red; */
		}

		.swiper-item {
			overflow-y: scroll;
			width: 99.5%;
			height: 99%;
			/* background-color: white; */
			/* height: 99%; */
			box-sizing: border-box;
			padding: 1rpx;
		}

	}
</style>