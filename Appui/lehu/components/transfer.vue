<template>
	<view class="warp">
		<u-loading-icon text="稍等正在加载"  color="red" textSize="18" :show="loading"></u-loading-icon>
		<my-toast ref="myToast"></my-toast>
		<view class="boxlist">
			<view>
				<u-alert title="默认资金密码为 666666" fontSize="24rpx" :center="true" type = "warning" :closable="closable"></u-alert>
				<u--form labelPosition="left" :model="transferlist" :rules="rules" ref="uForm" :errorType="errorType">
					<view class="sub_item"><u--text text="钱包余额:" type="info" size="24rpx"></u--text>
					<u--text :text="userInfo.money" size="48rpx" mode="price" type="error" :bold="true"></u--text></view>
					<view class="item">
						<view class="t1"><u-text type="info" text="转账UID:" size="24rpx" align="left"></u-text></view>
						<view class="t2">
							<u-form-item prop="touid">
								<u--input placeholder="请输入转账对方会员ID" border="none"  v-model="transferlist.touid" fontSize="26" inputAlign="center" style="height: 35rpx;"></u--input>
							</u-form-item>
						</view>
					</view>
					<view class="item">
						<view class="t1"><u-text type="info" text="转账金额:" size="24rpx" align="left"></u-text></view>
						<view class="t2">
							<u-form-item prop="amount">
								<u--input placeholder="请输入转账金额" border="none"  v-model="transferlist.amount" fontSize="26" inputAlign="center" style="height: 35rpx;"></u--input>
							</u-form-item>
						</view>
					</view>
					<view class="password">
						<u-modal :show="show" title="请输入支付密码"  showCancelButton @confirm="confirm" @cancel="cancel" :zoom="true" width="600rpx">
							<u-form-item  prop="zfpassword">
								<u--input placeholder="" type="password"  border="surround" inputAlign="center" v-model="transferlist.zfpassword" fontSize="26"  style="height: 35rpx;width:360rpx;"></u--input>
							</u-form-item>
						</u-modal>
					</view>
					<u-alert title="注意:转账后无法撤销, 请仔细核对,谨慎操作!!" fontSize="24rpx"   type = "error"  :center="true"></u-alert>
					<view class="margin-top-20">
						<u-button type="warning" :disabled="disabled" text="确认转账" throttleTime="2000" :loading="loading1" loadingText="正在转账中" @click="openmodel"></u-button>
					</view>
				</u--form>
			</view>
		</view>
	</view>
</template>

<script>
	import MyToast from '@/components/MyToast.vue';
	export default {
		name:"transfer",
		components: {
		    MyToast
		},
		props:{
			toUid:{
				//转账对方 to Uid 
				type:String,
				default: '',
			}
		},
		
		data() {
			return {
				disabled:false,
				loading:false,
				loading1:false,
				show:false,
				closable:true,
				errorType:'toast',
				userInfo:{
					money:0,
					uid:'',
					username:'',
				},
				transferlist:{
					amount:'',
					touid:'',
					zfpassword:'',
				},
				rules:{
					amount:[
						{
							type: 'string',
							required: true,
							message: '请输入转账金额',
							trigger: ['blur', 'change']
						},
						{
							validator: (rule, value, callback) => {
								if (value && parseFloat(value) > this.userInfo.money) {
								  callback(new Error('转账金额不能大于余额'));
								}else if (value && parseFloat(value) < 5) {
								  callback(new Error('最低转账金额 5'));
								} else {
								  callback();
								}
							},
							trigger: ['blur', 'change'],
						},
						{
							validator: (rule, value, callback) => {
								return uni.$u.test.amount(value);
							},
							message: '金额填写不正确',
							trigger: ['blur', 'change'],
						},
					],
					touid:[
						{
							type: 'string',
							required: true,
							message: '请输入对方的ID',
							trigger: ['blur', 'change']
						},
						{
							// 自定义验证函数，见上说明
							validator: (rule, value, callback) => {
								// 上面有说，返回true表示校验通过，返回false表示不通过
								// uni.$u.test.mobile()就是返回true或者false的
								return uni.$u.test.digits(value);
							},
							message: '会员ID必须是数字',
							// 触发器可以同时用blur和change
							trigger: ['change','blur'],
						}
					],
					zfpassword:[
						{
							type: 'string',
							required: true,
							message: '请输入支付密码',
							trigger: ['blur', 'change']
						}
					],
				},
			};
		},
		
		mounted() {
			this.myinfo();
			if(this.toUid){
				this.transferlist.touid = this.toUid;
			}
		},
		methods:{
			myinfo(){
				uni.$u.http.post('/api/user/userinfo').then(res => {
					if(res.code !== 200){
						this.$refs.myToast.Error(res.msg);
						return false;
					}
					this.userInfo.uid = res.data.uid;
					this.userInfo.username = res.data.username;
					this.userInfo.money = res.data.money;
					console.log('res: ',res);
				}).catch((err) =>{
					console.log('报错返回',err);
					this.$refs.myToast.Error(err);
				});
				
			},
			openmodel(){
				this.$refs.uForm.validate().then(rrr => {
					//弹出密码框
					this.show = true;
				}).catch(errors => {
					console.log('校验不通过');
				})
				
			},
			confirm(){
				this.$refs.uForm.validate().then(rrr => {
					//弹出密码框
					this.show = false;
					this.transfer();
				}).catch(errors => {
					console.log('校验不通过');
				})
			},
			cancel(){
				console.log('cancel');
				this.show = false;
			},
			transfer(){
				let data = {
					to_uid:this.transferlist.touid,
					amount:this.transferlist.amount,
					zfpassword:this.transferlist.zfpassword,
				};
				uni.$u.http.post('/api/wallet/transfer',data).then(res => {
					if(res.code !== 200){
						this.$refs.myToast.Error(res.msg);
						return false;
					}
					this.$refs.myToast.Success(res.msg);
					this.transferlist.zfpassword = '';
					console.log('res: ',res);
					//转账成功后，关闭弹窗
					setTimeout(()=>{
						this.$emit('close'); //触 发事件
					},2000);
					
				}).catch((err) =>{
					console.log('报错返回',err);
					this.$refs.myToast.Error(err);
					this.transferlist.zfpassword = '';
					this.transferlist.amount = '';
				});
			},
		},
	}
</script>

<style lang="scss">
.warp{
	width:80vw;
	display: flex;
	justify-content: center;
	flex-direction: column;
	align-items: center;
	.boxlist{
		width:80vw;
		margin-top:-20rpx;
		.tiprow{
			display: flex;
			flex-direction: row;
			width:80vw;
			justify-content: center;
			align-items: center;
			height:100rpx;
			line-height: 100rpx;
			padding-top:10rpx;
		}
		.item{
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
			padding:10rpx 10rpx;
			margin: 20rpx 0;
			border-bottom: 1px solid #eee;
			
			.t1 {
			    flex: 1;
			  }
			
			  /* u--input 的 flex 值 */
			.t2 {
			    flex: 3; /* 这个值可以根据你的需求进行调整 */
				text-align: center;
				.uni-input{
					width:370rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;
					flex-direction: row;
					font-size: 24rpx;
					flex-wrap: nowrap;
				}
			}
		}
		.sub_item{
			display: flex;
			flex-direction: row;
			align-items: center;
			padding:10rpx 10rpx;
			margin: 20rpx 0;
			border-bottom: 1px solid #eee;
		}
	}
}


</style>