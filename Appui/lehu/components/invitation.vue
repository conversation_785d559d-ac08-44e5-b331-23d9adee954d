<template>
	<view>
		<view class="main">
			<view><u--text type="error" size="24rpx" text="扫描二维码下载游戏"></u--text></view>
			<view class="uqrcode">
				<view class="qrcode"><uqrcode ref="uqrcode" canvas-id="qrcode" :value="link" size=130 :options="{ margin: 10, }"></uqrcode></view>
			</view>
			<view class="codebox">
				<view class="code"><u--input placeholder="邀请码" class="input"  border="surround" v-model="invite_code" ></u--input></view>
				<view class="copy"><u-button type="primary" size="small" text="复制邀请码" @click="copy(invite_code)"></u-button></view>
			</view>
			<view class="linkbox">
				<view class="link"><u--input placeholder="邀请链接" class="input"  border="surround" v-model="link" ></u--input></view>
				<view class="copy"><u-button type="primary" size="small" text="复制链接" @click="copy(link)"></u-button></view>
			</view>
		</view>
	</view>
</template>

<script>

	export default {
		name:"invitation",
		data() {
			return {
				text: 'uQRCode',
				textSync: '',
				textSync2: '',
				loadingSync: true,
				loadingSync2: true,
				size: 100,
				link:'',
				invite_code:'',
			};
		},
		onReady() {

		},
		mounted() {
			this.myinfo();
			this.mycfg();
		},
		methods: {
			gopage(url) {
				this.$Router.push({ name: url });
			},
			saveQrcode(){
				this.$refs.uqrcode.save({
				  success: () => {
				    uni.showToast({
				      icon: 'success',
				      title: '保存成功'
				    });
				  }
				});
			},
			copy(con) {
				//uni.setClipboardData 内容复制到粘贴板
				uni.setClipboardData({
					data: con, //要被复制的内容
					success: () => {
						//复制成功的回调函数
						uni.showToast({
							//提示
							title: '复制成功'
						});
					}
				});
			},
			myinfo(){
				uni.$u.http.post('/api/user/userinfo').then(res => {
					if(res.code !== 200){
						this.$refs.myToast.Error(res.msg);
						return false;
					}
					this.invite_code = res.data.invite_code;
					console.log('res: ',res);
				}).catch((err) =>{
					console.log('报错返回',err);
					this.$refs.myToast.Error(err);
				});
				
			},
			mycfg(){
				uni.$u.http.post('/api/config/getconfig').then(res => {
					if(res.code !== 200){
						this.$refs.myToast.Error(res.msg);
						return false;
					}
					this.link = res.data.downurl;
					console.log('res: ',res);
				}).catch((err) =>{
					console.log('报错返回',err);
					this.$refs.myToast.Error(err);
				});
				
			},
		}
	}
</script>

<style lang="scss">
.main{
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	width:70vw;
	height:670rpx;
	position: relative;
	top:-80rpx;
	.uqrcode{
		width:56vw;
		.qrcode{
			display: flex; // 使用flex布局
			justify-content: center; // 水平居中
			align-items: center; // 垂直居中
		}
		.btn{
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
		}
	}
	.codebox{
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: row;
		padding:10rpx 10rpx;
		.input{
			height:40rpx;
			width:200rpx;
			margin-right: 20rpx;
		}
		
	}
	.linkbox{
		width:70vw;
		height:80rpx;
		line-height: 80rpx;
		margin-top:20rpx;
		.copy{
			margin-top:20rpx;
		}
	}
}
</style>