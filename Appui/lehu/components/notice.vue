<template>
	<view>
		<u-list class="listbox" :showScrollbar='true' height="550">
			<u-list-item class="list-item" v-for="(item,index) in noticeList" :key="index">
				<view class="title">{{item.title}}</view>
				<view class="content" v-html="item.content"></view>
				<view class="time">{{item.created_at}}</view>
			</u-list-item>
		</u-list>
	</view>
</template>

<script>
	export default {
		name: "notice",
		data() {
			return {
				noticeList: [{
					title: '',
					content: '',
					created_at: ''
				}],

			};
		},
		mounted() {
			this.allNotice();
		},
		methods: {
			gopage(url) {
				console.log(url);
			},

			allNotice() {
				uni.$u.http.post('/api/game/getGameNotice', {
					limit: 10
				}).then(res => {
					if (res.code !== 200) {
						this.$refs.myToast.Error(res.msg);
						return false;
					}
					this.noticeList = res.data;
					console.log('noticeList: ', res);
				}).catch((err) => {
					console.log('报错noticelist', err);
					this.$refs.myToast.Error(err);
				});
			},
		},
	}
</script>

<style lang="scss">
	.u-list {
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		margin-top: 90rpx;
		height: 500rpx;
	}

	.listbox {
		display: flex;
		position: relative;
		top: -100rpx;
	}

	.list-item {
		width: 75vw;
		padding: 20rpx 20rpx;
		margin-top: 20rpx;
		border: 1px solid #cccccc;

		.title {
			height: 46rpx;
			width: 100%;
			font-weight: bold;
		}

		.content {
			font-size: 14px;
			line-height: 50rpx;
			padding: 10rpx 10rpx;
			border: 0 solid #f6f6f6;
			margin-top: 16rpx;
		}

		.time {
			font-size: 12px;
			color: #ffffff;
			text-align: center;
			background-color: #ffbe55;

		}
	}
</style>