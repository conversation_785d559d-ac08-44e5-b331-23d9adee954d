#!/bin/bash
# YouTube Twitter爬虫启动脚本
# 作者: GreenJoson
# 创建时间: 2025-06-26

# 设置脚本错误时退出
set -e

# 项目根目录
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$PROJECT_DIR"

# 虚拟环境路径
VENV_PATH="/Users/<USER>/Projects/Python/venv"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查虚拟环境
check_venv() {
    if [ ! -d "$VENV_PATH" ]; then
        print_message $RED "错误: 虚拟环境不存在: $VENV_PATH"
        print_message $YELLOW "请先创建虚拟环境:"
        print_message $YELLOW "python3 -m venv $VENV_PATH"
        exit 1
    fi
}

# 激活虚拟环境
activate_venv() {
    print_message $BLUE "激活虚拟环境: $VENV_PATH"
    source "$VENV_PATH/bin/activate"
}

# 安装依赖
install_deps() {
    print_message $BLUE "检查并安装依赖包..."
    pip install -r requirements.txt
}

# 运行测试
run_tests() {
    print_message $BLUE "运行测试..."
    python test_v1.py
    if [ $? -eq 0 ]; then
        print_message $GREEN "✓ 测试通过"
    else
        print_message $RED "✗ 测试失败"
        exit 1
    fi
}

# 创建代理文件
create_proxy_file() {
    if [ ! -f "proxy_list.txt" ]; then
        print_message $YELLOW "创建代理配置文件..."
        cat > proxy_list.txt << EOF
# 代理配置文件
# 请添加您的代理服务器，格式如下:
# ip:port
# username:password@ip:port
# http://ip:port

# 示例（请替换为真实代理）:
# 127.0.0.1:8080
# user:<EMAIL>:3128
EOF
        print_message $YELLOW "已创建 proxy_list.txt，请编辑添加代理服务器"
    fi
}

# 创建环境配置文件
create_env_file() {
    if [ ! -f ".env" ]; then
        print_message $YELLOW "创建环境配置文件..."
        cp .env.example .env
        print_message $YELLOW "已创建 .env 文件，可根据需要修改配置"
    fi
}

# 显示帮助信息
show_help() {
    echo "YouTube Twitter爬虫启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  install     安装依赖包"
    echo "  test        运行测试"
    echo "  once        单次运行"
    echo "  start       持续运行（默认）"
    echo "  setup       初始化设置"
    echo "  help        显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 setup     # 初始化设置"
    echo "  $0 test      # 运行测试"
    echo "  $0 once      # 单次运行"
    echo "  $0 start     # 持续运行"
}

# 初始化设置
setup() {
    print_message $GREEN "开始初始化设置..."
    
    check_venv
    activate_venv
    install_deps
    create_proxy_file
    create_env_file
    
    print_message $GREEN "✓ 初始化设置完成"
    print_message $YELLOW "请编辑 proxy_list.txt 添加代理服务器"
    print_message $YELLOW "可选: 编辑 .env 文件修改配置"
}

# 主函数
main() {
    case "${1:-start}" in
        "install")
            check_venv
            activate_venv
            install_deps
            ;;
        "test")
            check_venv
            activate_venv
            run_tests
            ;;
        "once")
            check_venv
            activate_venv
            print_message $GREEN "开始单次运行..."
            python main.py --mode once
            ;;
        "start")
            check_venv
            activate_venv
            print_message $GREEN "开始持续运行..."
            python main.py --mode continuous
            ;;
        "setup")
            setup
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_message $RED "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
