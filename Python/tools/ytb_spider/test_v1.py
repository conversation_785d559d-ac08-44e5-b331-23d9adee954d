#!/usr/bin/env python3
"""
YouTube Twitter爬虫测试文件 v1
测试各个模块的功能是否正常

测试内容:
1. 数据库操作测试
2. 代理管理测试
3. Twitter链接提取测试
4. 爬虫功能测试
5. 工具函数测试

作者: GreenJoson
创建时间: 2025-06-26
版本: v1
"""

import sys
import unittest
from pathlib import Path
from unittest.mock import Mock, patch

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from database import DatabaseManager
from proxy_manager import ProxyManager
from utils import TwitterExtractor, TextProcessor, URLValidator
from spider import YouTubeSpider
from config import create_directories


class TestDatabaseManager(unittest.TestCase):
    """数据库管理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.db = DatabaseManager(':memory:')  # 使用内存数据库
    
    def test_database_initialization(self):
        """测试数据库初始化"""
        # 检查表是否创建成功
        with self.db.db_path as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            expected_tables = ['channels', 'crawl_logs', 'proxy_status']
            for table in expected_tables:
                self.assertIn(table, tables, f"表 {table} 未创建")
    
    def test_insert_and_get_channel(self):
        """测试频道数据插入和查询"""
        # 测试数据
        test_channel = {
            'channel_id': 'test_channel_123',
            'channel_url': 'https://youtube.com/channel/test_channel_123',
            'channel_name': '测试频道',
            'subscriber_count': 10000,
            'video_count': 100,
            'description': '这是一个测试频道',
            'twitter_links': ['https://x.com/testuser']
        }
        
        # 插入数据
        result = self.db.insert_channel(test_channel)
        self.assertTrue(result, "频道数据插入失败")
        
        # 查询数据
        retrieved_channel = self.db.get_channel_by_id('test_channel_123')
        self.assertIsNotNone(retrieved_channel, "查询频道数据失败")
        self.assertEqual(retrieved_channel['channel_name'], '测试频道')
        self.assertEqual(len(retrieved_channel['twitter_links']), 1)
    
    def test_get_channels_with_twitter(self):
        """测试获取包含Twitter链接的频道"""
        # 插入测试数据
        channels_data = [
            {
                'channel_id': 'channel_with_twitter',
                'channel_url': 'https://youtube.com/channel/channel_with_twitter',
                'channel_name': '有Twitter的频道',
                'twitter_links': ['https://x.com/user1']
            },
            {
                'channel_id': 'channel_without_twitter',
                'channel_url': 'https://youtube.com/channel/channel_without_twitter',
                'channel_name': '没有Twitter的频道',
                'twitter_links': []
            }
        ]
        
        for channel in channels_data:
            self.db.insert_channel(channel)
        
        # 查询包含Twitter的频道
        twitter_channels = self.db.get_channels_with_twitter()
        self.assertEqual(len(twitter_channels), 1, "应该只有一个包含Twitter的频道")
        self.assertEqual(twitter_channels[0]['channel_name'], '有Twitter的频道')


class TestTwitterExtractor(unittest.TestCase):
    """Twitter链接提取器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.extractor = TwitterExtractor()
    
    def test_extract_twitter_links(self):
        """测试Twitter链接提取"""
        test_texts = [
            "Follow me on Twitter: @testuser",
            "Check out https://twitter.com/example",
            "Visit my X profile: https://x.com/myprofile",
            "Contact me at @user123 or <EMAIL>",
            "Multiple links: @user1 https://x.com/user2 twitter.com/user3"
        ]
        
        for text in test_texts:
            links = self.extractor.extract_twitter_links(text)
            self.assertIsInstance(links, list, "返回结果应该是列表")
            if links:
                for link in links:
                    self.assertTrue(link.startswith('https://x.com/'), 
                                  f"链接格式不正确: {link}")
    
    def test_is_valid_twitter_username(self):
        """测试Twitter用户名验证"""
        valid_usernames = ['testuser', 'user123', 'test_user', 'a']
        invalid_usernames = ['', 'toolongusernamethatexceeds15chars', 'user@name', 'twitter']
        
        for username in valid_usernames:
            self.assertTrue(self.extractor.is_valid_twitter_username(username),
                          f"用户名应该有效: {username}")
        
        for username in invalid_usernames:
            self.assertFalse(self.extractor.is_valid_twitter_username(username),
                           f"用户名应该无效: {username}")
    
    def test_normalize_twitter_link(self):
        """测试Twitter链接标准化"""
        test_cases = [
            ('testuser', 'https://x.com/testuser'),
            ('@testuser', 'https://x.com/testuser'),
            ('  testuser  ', 'https://x.com/testuser'),
            ('invalid@user', None),
            ('', None)
        ]
        
        for input_username, expected_output in test_cases:
            result = self.extractor.normalize_twitter_link(input_username)
            self.assertEqual(result, expected_output,
                           f"标准化失败: {input_username} -> {result} (期望: {expected_output})")


class TestTextProcessor(unittest.TestCase):
    """文本处理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.processor = TextProcessor()
    
    def test_extract_numbers(self):
        """测试数字提取"""
        test_cases = [
            ('1.2M subscribers', 1200000),
            ('500K views', 500000),
            ('1,234,567', 1234567),
            ('2.5B followers', 2500000000),
            ('100 videos', 100),
            ('no numbers here', None),
            ('', None)
        ]
        
        for text, expected in test_cases:
            result = self.processor.extract_numbers(text)
            self.assertEqual(result, expected,
                           f"数字提取失败: '{text}' -> {result} (期望: {expected})")
    
    def test_clean_text(self):
        """测试文本清理"""
        test_cases = [
            ('  多余的   空格  ', '多余的 空格'),
            ('<p>HTML标签</p>', 'HTML标签'),
            ('特殊字符!@#$%^&*()', '特殊字符!'),
            ('', ''),
        ]
        
        for input_text, expected in test_cases:
            result = self.processor.clean_text(input_text)
            self.assertEqual(result, expected,
                           f"文本清理失败: '{input_text}' -> '{result}' (期望: '{expected}')")


class TestURLValidator(unittest.TestCase):
    """URL验证器测试类"""
    
    def test_is_valid_youtube_url(self):
        """测试YouTube URL验证"""
        valid_urls = [
            'https://youtube.com/channel/test',
            'https://www.youtube.com/c/test',
            'https://m.youtube.com/@test'
        ]
        
        invalid_urls = [
            'https://google.com',
            'https://twitter.com/test',
            '',
            'not_a_url'
        ]
        
        for url in valid_urls:
            self.assertTrue(URLValidator.is_valid_youtube_url(url),
                          f"URL应该有效: {url}")
        
        for url in invalid_urls:
            self.assertFalse(URLValidator.is_valid_youtube_url(url),
                           f"URL应该无效: {url}")
    
    def test_extract_channel_id(self):
        """测试频道ID提取"""
        test_cases = [
            ('https://youtube.com/channel/UC123456', 'UC123456'),
            ('https://youtube.com/c/testchannel', 'testchannel'),
            ('https://youtube.com/@username', '@username'),
            ('https://youtube.com/user/olduser', 'olduser'),
            ('invalid_url', None)
        ]
        
        for url, expected in test_cases:
            result = URLValidator.extract_channel_id(url)
            self.assertEqual(result, expected,
                           f"频道ID提取失败: {url} -> {result} (期望: {expected})")


class TestProxyManager(unittest.TestCase):
    """代理管理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时代理文件
        self.temp_proxy_file = Path('test_proxy_list.txt')
        with open(self.temp_proxy_file, 'w') as f:
            f.write("127.0.0.1:8080\n")
            f.write("user:<EMAIL>:3128\n")
            f.write("http://proxy2.example.com:8080\n")
    
    def tearDown(self):
        """测试后清理"""
        if self.temp_proxy_file.exists():
            self.temp_proxy_file.unlink()
    
    def test_parse_proxy_line(self):
        """测试代理配置解析"""
        proxy_manager = ProxyManager()
        
        test_cases = [
            ('127.0.0.1:8080', {'http': 'http://127.0.0.1:8080', 'https': 'http://127.0.0.1:8080'}),
            ('user:<EMAIL>:3128', {'http': 'http://user:<EMAIL>:3128', 'https': 'http://user:<EMAIL>:3128'}),
            ('http://proxy.com:8080', {'http': 'http://proxy.com:8080', 'https': 'http://proxy.com:8080'}),
            ('invalid_format', None)
        ]
        
        for proxy_line, expected in test_cases:
            result = proxy_manager.parse_proxy_line(proxy_line)
            self.assertEqual(result, expected,
                           f"代理解析失败: {proxy_line} -> {result} (期望: {expected})")


def run_integration_test():
    """运行集成测试"""
    print("\n" + "="*60)
    print("运行集成测试...")
    print("="*60)
    
    try:
        # 创建必要目录
        create_directories()
        print("✓ 目录创建成功")
        
        # 测试数据库连接
        db = DatabaseManager(':memory:')
        print("✓ 数据库连接成功")
        
        # 测试代理管理器
        proxy_manager = ProxyManager()
        print("✓ 代理管理器初始化成功")
        
        # 测试爬虫初始化
        spider = YouTubeSpider()
        print("✓ 爬虫初始化成功")
        
        print("\n集成测试通过！所有组件正常工作。")
        
    except Exception as e:
        print(f"\n集成测试失败: {e}")
        return False
    
    return True


def main():
    """主测试函数"""
    print("YouTube Twitter爬虫测试 v1")
    print("="*60)
    
    # 运行单元测试
    print("运行单元测试...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 运行集成测试
    run_integration_test()
    
    print("\n测试完成！")


if __name__ == '__main__':
    main()
