"""
YouTube爬虫配置文件
包含所有配置参数和常量定义
"""

import os
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent

# 数据库配置
DATABASE_CONFIG = {
    'db_path': PROJECT_ROOT / 'youtube_channels.db',
    'backup_interval': 3600,  # 备份间隔(秒)
}

# 爬虫配置
SPIDER_CONFIG = {
    'request_delay': (2, 5),  # 请求间隔范围(秒)
    'max_retries': 3,  # 最大重试次数
    'timeout': 30,  # 请求超时时间(秒)
    'concurrent_requests': 5,  # 并发请求数
    'max_channels_per_run': 100,  # 每次运行最大爬取频道数
}

# 代理配置
PROXY_CONFIG = {
    'enabled': True,  # 是否启用代理
    'proxy_list_file': PROJECT_ROOT / 'proxy_list.txt',
    'proxy_test_url': 'https://httpbin.org/ip',
    'proxy_timeout': 10,  # 代理测试超时时间
    'max_proxy_failures': 3,  # 代理最大失败次数
}

# User-Agent列表
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
]

# YouTube相关配置
YOUTUBE_CONFIG = {
    'base_url': 'https://www.youtube.com',
    'channel_url_patterns': [
        r'https://www\.youtube\.com/channel/([a-zA-Z0-9_-]+)',
        r'https://www\.youtube\.com/c/([a-zA-Z0-9_-]+)',
        r'https://www\.youtube\.com/@([a-zA-Z0-9_.-]+)',
        r'https://www\.youtube\.com/user/([a-zA-Z0-9_-]+)',
    ],
    'search_keywords': [
        'gaming', 'tech', 'music', 'comedy', 'education', 'news',
        'entertainment', 'sports', 'cooking', 'travel', 'fashion',
        'beauty', 'fitness', 'diy', 'art', 'science'
    ],
}

# Twitter/X链接识别配置
TWITTER_CONFIG = {
    'patterns': [
        r'https?://(?:www\.)?twitter\.com/([a-zA-Z0-9_]+)',
        r'https?://(?:www\.)?x\.com/([a-zA-Z0-9_]+)',
        r'@([a-zA-Z0-9_]+)',  # @username格式
    ],
    'domains': ['twitter.com', 'x.com'],
}

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',
    'format': '{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}',
    'rotation': '10 MB',
    'retention': '7 days',
    'log_file': PROJECT_ROOT / 'logs' / 'spider.log',
}

# 创建必要的目录
def create_directories():
    """创建项目所需的目录"""
    directories = [
        PROJECT_ROOT / 'logs',
        PROJECT_ROOT / 'data',
        PROJECT_ROOT / 'backups',
    ]
    
    for directory in directories:
        directory.mkdir(exist_ok=True)

# 环境变量配置
def load_env_config():
    """加载环境变量配置"""
    from dotenv import load_dotenv
    load_dotenv()
    
    # YouTube API密钥(可选)
    YOUTUBE_API_KEY = os.getenv('YOUTUBE_API_KEY')
    
    # 代理配置
    PROXY_USERNAME = os.getenv('PROXY_USERNAME')
    PROXY_PASSWORD = os.getenv('PROXY_PASSWORD')
    
    return {
        'youtube_api_key': YOUTUBE_API_KEY,
        'proxy_auth': {
            'username': PROXY_USERNAME,
            'password': PROXY_PASSWORD,
        } if PROXY_USERNAME and PROXY_PASSWORD else None,
    }

if __name__ == '__main__':
    create_directories()
    print("配置文件加载完成，目录结构已创建")
