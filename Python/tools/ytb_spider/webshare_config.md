# Webshare.io 代理配置指南

## 关于 Webshare.io

Webshare.io 是一个高质量的代理服务提供商，提供：
- 高速住宅代理和数据中心代理
- 99.9% 正常运行时间
- 全球多个地区的IP
- 支持HTTP/HTTPS和SOCKS5协议
- 用户友好的仪表板

## 配置步骤

### 1. 获取 Webshare 代理信息

1. 登录您的 Webshare.io 账户
2. 进入 "Proxy" 或"代理列表" 页面
3. 复制代理服务器信息，格式通常为：
   ```
   IP:端口:用户名:密码
   ```

### 2. 配置代理文件

在项目根目录创建或编辑 `proxy_list.txt` 文件：

```bash
# Webshare.io 代理配置示例

# 格式1: username:password@ip:port
your_username:your_password@proxy_ip:proxy_port

# 格式2: ***************************:port
*******************************************:proxy_port

# 实际示例（请替换为您的真实信息）:
# user123:pass456@*************:8000
# *****************************************
# user123:pass456@*************:8000

# 多个代理示例
# webshare_user1:webshare_pass1@************:8080
# webshare_user1:webshare_pass1@************:8080
# webshare_user1:webshare_pass1@************:8080
```

### 3. 环境变量配置（可选）

如果所有代理使用相同的用户名和密码，可以在 `.env` 文件中配置：

```bash
# Webshare 代理认证信息
PROXY_USERNAME=your_webshare_username
PROXY_PASSWORD=your_webshare_password
```

### 4. 批量导入 Webshare 代理

如果您有大量代理，可以使用以下Python脚本批量转换格式：

```python
#!/usr/bin/env python3
"""
Webshare代理格式转换脚本
将Webshare导出的代理列表转换为本项目支持的格式
"""

def convert_webshare_format(input_file, output_file, username, password):
    """
    转换Webshare代理格式

    Args:
        input_file: Webshare导出的代理文件（格式：ip:port）
        output_file: 输出文件
        username: Webshare用户名
        password: Webshare密码
    """
    with open(input_file, 'r') as f:
        lines = f.readlines()

    with open(output_file, 'w') as f:
        f.write("# Webshare.io 代理配置\n")
        f.write("# 自动生成，请勿手动编辑\n\n")

        for line in lines:
            line = line.strip()
            if line and not line.startswith('#'):
                # 假设格式为 ip:port
                if ':' in line:
                    ip_port = line.strip()
                    proxy_line = f"{username}:{password}@{ip_port}\n"
                    f.write(proxy_line)

# 使用示例
if __name__ == '__main__':
    # 请替换为您的实际信息
    USERNAME = "your_webshare_username"
    PASSWORD = "your_webshare_password"

    convert_webshare_format(
        'webshare_proxies.txt',  # Webshare导出的文件
        'proxy_list.txt',        # 输出文件
        USERNAME,
        PASSWORD
    )
    print("代理格式转换完成！")
```

## 测试代理连接

使用以下命令测试代理是否正常工作：

```bash
# 运行代理测试
python -c "
from proxy_manager import ProxyManager
pm = ProxyManager()
proxy = pm.get_random_proxy()
if proxy:
    is_working, response_time = pm.test_proxy(proxy)
    print(f'代理测试结果: 可用={is_working}, 响应时间={response_time:.2f}秒')
else:
    print('没有可用的代理')
"
```

## Webshare 代理类型说明

### 1. 动态住宅代理 ⭐ **推荐用于YouTube爬虫**
- 真实住宅IP，自动轮换
- 最难被YouTube检测和封禁
- 成功率最高（90%+）
- 价格较高但物有所值

### 2. 静态住宅代理
- 固定住宅IP地址
- 长期稳定，适合需要固定IP的场景
- 比动态代理便宜，但轮换性差

### 3. 数据中心代理 ⚠️ **不推荐用于YouTube**
- 速度快，价格便宜
- 容易被YouTube识别和限制
- 封禁率较高，不适合长期爬取

## 推荐配置

### 动态住宅代理配置（推荐）

```python
# config.py 中的代理配置
PROXY_CONFIG = {
    'enabled': True,
    'proxy_timeout': 20,  # 住宅代理可能稍慢，增加超时时间
    'max_proxy_failures': 3,  # 住宅代理偶尔不稳定，允许更多失败
    'rotation_interval': 5,   # 更频繁轮换，利用动态特性
}

# 请求配置 - 针对住宅代理优化
SPIDER_CONFIG = {
    'request_delay': (4, 8),  # 住宅代理需要更长间隔，模拟真实用户
    'max_retries': 3,  # 住宅代理可能有网络波动，增加重试
    'concurrent_requests': 2,  # 降低并发，避免触发限制
    'max_channels_per_run': 80,  # 适中的爬取量
}
```

### 数据中心代理配置（不推荐，仅供参考）

```python
# 如果必须使用数据中心代理
PROXY_CONFIG = {
    'enabled': True,
    'proxy_timeout': 10,
    'max_proxy_failures': 1,  # 数据中心代理失败率高，快速切换
    'rotation_interval': 3,   # 频繁轮换避免被封
}

SPIDER_CONFIG = {
    'request_delay': (8, 15), # 大幅增加延迟，降低被检测风险
    'max_retries': 1,
    'concurrent_requests': 1, # 单线程，最保守的方式
    'max_channels_per_run': 30,
}
```

## 故障排除

### 1. 代理连接失败
- 检查用户名和密码是否正确
- 确认代理IP和端口是否有效
- 检查Webshare账户余额和流量

### 2. 认证失败
- 确认代理认证信息格式正确
- 检查是否超出并发连接限制
- 验证IP白名单设置（如果启用）

### 3. 速度较慢
- 选择地理位置更近的代理
- 使用数据中心代理而非住宅代理
- 检查网络连接质量

## 监控和统计

爬虫会自动记录代理使用统计：

```bash
# 查看代理统计
python -c "
from proxy_manager import ProxyManager
pm = ProxyManager()
stats = pm.get_proxy_stats()
print('代理统计:', stats)
"
```

## 成本优化建议

1. **合理设置请求间隔**：避免过快请求导致代理被封
2. **使用代理轮换**：避免单个代理过载
3. **监控流量使用**：及时了解代理消耗情况
4. **选择合适的代理类型**：根据需求选择数据中心或住宅代理

## 联系支持

如果遇到Webshare相关问题：
- Webshare官方支持：https://webshare.io/support
- 项目Issues：在GitHub项目中提交问题

---

**注意**：请确保遵守Webshare的使用条款和YouTube的robots.txt规则。
