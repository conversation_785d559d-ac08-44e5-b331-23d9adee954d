# 🚀 Webshare.io 快速开始指南

使用 Webshare.io 代理服务快速启动 YouTube Twitter 爬虫。

## 📋 准备工作

### 1. Webshare.io 账户
- 注册 [Webshare.io](https://webshare.io) 账户
- 购买代理套餐（推荐数据中心代理用于爬虫）
- 获取代理用户名和密码

### 2. 环境准备
```bash
# 激活虚拟环境
source /Users/<USER>/Projects/Python/venv/bin/activate

# 进入项目目录
cd /Users/<USER>/Projects/Python/tools/ytb_spider

# 安装依赖
pip install -r requirements.txt
```

## ⚡ 5分钟快速配置

### 方式1: 交互式配置（推荐新手）

```bash
# 运行交互式配置工具
python webshare_setup.py -i
```

按照提示输入：
1. Webshare 用户名
2. Webshare 密码  
3. 选择配置方式（手动输入或文件导入）
4. 输入代理端点或导入文件
5. 测试代理连接

### 方式2: 文件批量导入（推荐高级用户）

1. **从 Webshare 导出代理列表**
   - 登录 Webshare.io 控制台
   - 进入 "Proxy List" 页面
   - 复制代理端点列表，保存为 `webshare_proxies.txt`

2. **批量转换格式**
   ```bash
   python webshare_setup.py -c webshare_proxies.txt -u 你的用户名 -p 你的密码
   ```

3. **测试代理连接**
   ```bash
   python webshare_setup.py -t
   ```

### 方式3: 手动配置

创建 `proxy_list.txt` 文件：
```
# Webshare.io 代理配置
your_username:your_password@proxy_ip1:port1
your_username:your_password@proxy_ip2:port2
your_username:your_password@proxy_ip3:port3
```

## 🏃‍♂️ 运行爬虫

### 测试运行
```bash
# 运行测试确保一切正常
python test_v1.py

# 单次运行测试
python main.py --mode once
```

### 持续运行
```bash
# 后台持续运行
nohup python main.py --mode continuous > crawler.log 2>&1 &

# 或使用启动脚本
./run.sh start
```

## 📊 监控和管理

### 查看运行状态
```bash
# 查看日志
tail -f logs/spider.log

# 查看代理统计
python -c "
from proxy_manager import ProxyManager
pm = ProxyManager()
print('代理统计:', pm.get_proxy_stats())
"

# 查看数据库统计
python -c "
from database import DatabaseManager
db = DatabaseManager()
print('数据库统计:', db.get_statistics())
"
```

### 查看发现的 Twitter 账号
```bash
# 查看所有包含 Twitter 的频道
python -c "
from database import DatabaseManager
db = DatabaseManager()
channels = db.get_channels_with_twitter()
for ch in channels[:10]:  # 显示前10个
    print(f'频道: {ch[\"channel_name\"]}')
    print(f'Twitter: {ch[\"twitter_links\"]}')
    print('---')
"
```

## ⚙️ 优化配置

### 针对 Webshare 的推荐配置

编辑 `config.py`：
```python
# 代理配置优化
PROXY_CONFIG = {
    'enabled': True,
    'proxy_timeout': 15,  # Webshare 响应快，可增加超时
    'max_proxy_failures': 2,  # 高质量代理，减少失败阈值
}

# 爬虫配置优化  
SPIDER_CONFIG = {
    'request_delay': (2, 4),  # 使用代理可减少延迟
    'max_retries': 2,  # Webshare 稳定，减少重试
    'concurrent_requests': 3,  # 可适当增加并发
    'max_channels_per_run': 150,  # 增加每轮爬取数量
}
```

### 环境变量配置

创建 `.env` 文件：
```bash
# Webshare 认证信息
PROXY_USERNAME=your_webshare_username
PROXY_PASSWORD=your_webshare_password

# 爬虫优化配置
MAX_CHANNELS_PER_RUN=150
REQUEST_DELAY_MIN=2
REQUEST_DELAY_MAX=4
ENABLE_PROXY=true
```

## 🔧 故障排除

### 常见问题

1. **代理连接失败**
   ```bash
   # 测试单个代理
   python webshare_setup.py -t
   
   # 检查认证信息
   grep -n "username\|password" proxy_list.txt
   ```

2. **爬取速度慢**
   - 检查代理地理位置，选择更近的服务器
   - 减少请求延迟时间
   - 增加并发请求数

3. **代理被封**
   - 增加请求间隔
   - 轮换更多代理
   - 检查 User-Agent 设置

### 调试模式

```bash
# 启用详细日志
export LOG_LEVEL=DEBUG
python main.py --mode once
```

## 💡 最佳实践

### 1. 代理管理
- 使用多个代理轮换
- 定期测试代理健康状态
- 监控代理流量使用

### 2. 请求控制
- 合理设置请求间隔（2-5秒）
- 避免过高的并发请求
- 使用随机 User-Agent

### 3. 数据管理
- 定期备份 SQLite 数据库
- 监控磁盘空间使用
- 清理过期日志文件

### 4. 成本控制
- 监控 Webshare 流量消耗
- 优化爬取策略，避免重复请求
- 根据需求选择合适的代理套餐

## 📈 扩展功能

### 自动化部署
```bash
# 创建系统服务（Linux）
sudo tee /etc/systemd/system/ytb-spider.service > /dev/null <<EOF
[Unit]
Description=YouTube Twitter Spider
After=network.target

[Service]
Type=simple
User=your_username
WorkingDirectory=/path/to/ytb_spider
ExecStart=/Users/<USER>/Projects/Python/venv/bin/python main.py --mode continuous
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 启动服务
sudo systemctl enable ytb-spider
sudo systemctl start ytb-spider
```

### 监控脚本
```bash
# 创建监控脚本
cat > monitor.sh << 'EOF'
#!/bin/bash
# 检查爬虫运行状态
if ! pgrep -f "main.py" > /dev/null; then
    echo "爬虫已停止，正在重启..."
    cd /path/to/ytb_spider
    source /Users/<USER>/Projects/Python/venv/bin/activate
    nohup python main.py --mode continuous > crawler.log 2>&1 &
fi
EOF

chmod +x monitor.sh

# 添加到 crontab（每5分钟检查一次）
echo "*/5 * * * * /path/to/monitor.sh" | crontab -
```

## 🎯 预期结果

运行成功后，您将获得：
- 自动发现的 YouTube 频道列表
- 提取的 Twitter/X 账号信息
- 完整的爬取日志和统计
- 可查询的 SQLite 数据库

典型的发现率：
- 每小时发现 50-100 个新频道
- Twitter 链接发现率约 15-25%
- 代理轮换确保稳定运行

## 📞 获取帮助

- 查看详细文档：`README.md`
- Webshare 配置：`webshare_config.md`
- 项目问题：GitHub Issues
- Webshare 支持：https://webshare.io/support

---

**开始您的 YouTube Twitter 发现之旅！** 🚀
