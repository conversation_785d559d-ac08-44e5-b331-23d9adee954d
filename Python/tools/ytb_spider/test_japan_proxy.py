#!/usr/bin/env python3
"""
测试日本代理连接
使用正确的日本代理用户名格式

作者: GreenJoson
创建时间: 2025-06-26
"""

import requests
import time
import random

def test_japan_http_proxies():
    """测试日本HTTP代理"""
    print("🇯🇵 测试日本HTTP代理")
    print("=" * 50)
    
    # 日本HTTP代理配置
    japan_proxies = [
        "gorkpiln-JP-1:<EMAIL>:80",
        "gorkpiln-JP-100:<EMAIL>:80", 
        "gorkpiln-JP-500:<EMAIL>:80",
        f"gorkpiln-JP-{random.randint(1, 3964)}:<EMAIL>:80"
    ]
    
    working_proxies = []
    
    for i, proxy_auth in enumerate(japan_proxies, 1):
        print(f"\n[{i}/{len(japan_proxies)}] 测试代理: {proxy_auth}")
        
        proxy_url = f"http://{proxy_auth}"
        proxies = {
            "http": proxy_url,
            "https": proxy_url
        }
        
        try:
            start_time = time.time()
            response = requests.get(
                "http://ip-api.com/json/",
                proxies=proxies,
                timeout=20,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
                }
            )
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                
                print(f"  ✅ 连接成功!")
                print(f"    代理IP: {data.get('query')}")
                print(f"    位置: {data.get('city')}, {data.get('country')}")
                print(f"    国家代码: {data.get('countryCode')}")
                print(f"    响应时间: {response_time:.2f}秒")
                
                if data.get('countryCode') == 'JP':
                    print(f"    🎯 确认为日本IP!")
                    working_proxies.append({
                        "proxy": proxy_auth,
                        "ip": data.get('query'),
                        "location": f"{data.get('city')}, {data.get('country')}"
                    })
                else:
                    print(f"    ⚠️  非日本IP: {data.get('country')}")
                    
            else:
                print(f"  ❌ 连接失败 - 状态码: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ 连接异常: {str(e)[:100]}...")
        
        time.sleep(3)  # 避免请求过快
    
    return working_proxies

def test_japan_socks5_proxy():
    """测试日本SOCKS5代理"""
    print("\n🇯🇵 测试日本SOCKS5代理")
    print("=" * 50)
    
    # SOCKS5代理配置
    socks5_url = "socks5://gorkpiln:<EMAIL>:1080"
    
    proxies = {
        "http": socks5_url,
        "https": socks5_url
    }
    
    print(f"测试SOCKS5代理: {socks5_url}")
    
    try:
        start_time = time.time()
        response = requests.get(
            "http://ip-api.com/json/",
            proxies=proxies,
            timeout=20,
            headers={
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
            }
        )
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"  ✅ SOCKS5连接成功!")
            print(f"    代理IP: {data.get('query')}")
            print(f"    位置: {data.get('city')}, {data.get('country')}")
            print(f"    国家代码: {data.get('countryCode')}")
            print(f"    响应时间: {response_time:.2f}秒")
            
            if data.get('countryCode') == 'JP':
                print(f"    🎯 确认为日本IP!")
                return {
                    "success": True,
                    "ip": data.get('query'),
                    "location": f"{data.get('city')}, {data.get('country')}"
                }
            else:
                print(f"    ⚠️  非日本IP: {data.get('country')}")
                
        else:
            print(f"  ❌ SOCKS5连接失败 - 状态码: {response.status_code}")
            
    except Exception as e:
        print(f"  ❌ SOCKS5连接异常: {str(e)[:100]}...")
    
    return {"success": False}

def test_youtube_access(proxy_config):
    """测试通过代理访问YouTube"""
    print(f"\n🎯 测试YouTube访问...")
    
    try:
        response = requests.get(
            "https://www.youtube.com/@MrBeast",
            proxies=proxy_config,
            timeout=25,
            headers={
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }
        )
        
        if response.status_code == 200:
            print(f"  ✅ YouTube访问成功!")
            print(f"    页面大小: {len(response.text):,} 字符")
            
            # 检查是否包含Twitter链接
            content = response.text.lower()
            if 'twitter.com' in content or 'x.com' in content:
                print(f"  🐦 发现Twitter链接!")
            else:
                print(f"  ⚠️  未发现Twitter链接")
                
            return True
        else:
            print(f"  ❌ YouTube访问失败 - 状态码: {response.status_code}")
            
    except Exception as e:
        print(f"  ❌ YouTube访问异常: {str(e)[:80]}...")
    
    return False

def update_working_proxy_config(working_proxies):
    """更新工作代理配置"""
    if not working_proxies:
        return
    
    print(f"\n📝 更新代理配置文件...")
    
    # 选择第一个工作的代理
    best_proxy = working_proxies[0]
    
    # 更新proxy_list.txt，只保留工作的代理
    proxy_content = f"""# Webshare.io 日本代理配置 (已验证)
# 更新时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
# 限制国家: JP (日本)
# 验证状态: ✅ 已测试可用

# 工作的日本HTTP代理
"""
    
    for proxy_info in working_proxies:
        proxy_content += f"{proxy_info['proxy']}  # {proxy_info['location']}\n"
    
    # 添加SOCKS5代理
    proxy_content += f"\n# SOCKS5代理\nsocks5://gorkpiln:<EMAIL>:1080\n"
    
    with open("proxy_list.txt", "w", encoding="utf-8") as f:
        f.write(proxy_content)
    
    print(f"  ✅ 已更新 proxy_list.txt，包含 {len(working_proxies)} 个工作代理")
    
    # 更新.env文件
    env_content = f"""# Webshare.io 日本代理配置 (已验证)
# 更新时间: {time.strftime('%Y-%m-%d %H:%M:%S')}

# 最佳日本代理
WEBSHARE_USERNAME={best_proxy['proxy'].split(':')[0]}
WEBSHARE_PASSWORD=6lwffvf16jjn
WEBSHARE_ENDPOINT=p.webshare.io:80

# 代理配置
PROXY_USERNAME={best_proxy['proxy'].split(':')[0]}
PROXY_PASSWORD=6lwffvf16jjn
ENABLE_PROXY=true
PROXY_TIMEOUT=25

# 爬虫配置
MAX_CHANNELS_PER_RUN=50
REQUEST_DELAY_MIN=4
REQUEST_DELAY_MAX=8
CONCURRENT_REQUESTS=2

# 日志级别
LOG_LEVEL=INFO

# 数据库配置
DATABASE_URL=sqlite:///youtube_channels.db
"""
    
    with open(".env", "w", encoding="utf-8") as f:
        f.write(env_content)
    
    print(f"  ✅ 已更新 .env 文件")

def main():
    """主函数"""
    print("🇯🇵 日本代理连接测试")
    print("测试正确的日本代理用户名格式")
    print()
    
    # 1. 测试HTTP代理
    working_http_proxies = test_japan_http_proxies()
    
    # 2. 测试SOCKS5代理
    socks5_result = test_japan_socks5_proxy()
    
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    
    if working_http_proxies:
        print(f"✅ HTTP代理: 找到 {len(working_http_proxies)} 个工作代理")
        for proxy in working_http_proxies:
            print(f"   📍 {proxy['ip']} - {proxy['location']}")
        
        # 测试YouTube访问
        best_proxy = working_http_proxies[0]
        proxy_url = f"http://{best_proxy['proxy']}"
        proxy_config = {"http": proxy_url, "https": proxy_url}
        
        youtube_ok = test_youtube_access(proxy_config)
        
        if youtube_ok:
            print(f"✅ YouTube访问正常")
            
            # 更新配置文件
            update_working_proxy_config(working_http_proxies)
            
            print(f"\n🎉 日本代理配置完成!")
            print(f"💡 下一步:")
            print(f"1. 运行爬虫: python main.py --mode once")
            print(f"2. 测试Twitter映射: python test_twitter_mapping.py")
            
        else:
            print(f"⚠️  代理可用但YouTube访问有问题")
    else:
        print(f"❌ HTTP代理: 全部失败")
    
    if socks5_result["success"]:
        print(f"✅ SOCKS5代理: 工作正常 - {socks5_result['location']}")
    else:
        print(f"❌ SOCKS5代理: 连接失败")
    
    if not working_http_proxies and not socks5_result["success"]:
        print(f"\n💡 建议:")
        print(f"1. 检查VPN是否与代理冲突")
        print(f"2. 尝试不同的代理端口")
        print(f"3. 先用无代理模式: python test_no_proxy.py")

if __name__ == '__main__':
    main()
