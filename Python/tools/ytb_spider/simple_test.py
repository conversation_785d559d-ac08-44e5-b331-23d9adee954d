#!/usr/bin/env python3
"""
简单的Webshare代理测试
直接使用您提供的格式进行测试
"""

import requests

def test_webshare_proxy():
    """测试Webshare代理 - 使用您提供的确切格式"""
    print("🔍 测试Webshare代理连接...")
    
    # 使用您提供的确切格式
    proxy_url = "http://gorkpiln-1:<EMAIL>:80/"
    
    proxies = {
        "http": proxy_url,
        "https": proxy_url
    }
    
    print(f"代理URL: {proxy_url}")
    
    try:
        # 测试1: 使用您示例中的URL
        print("\n测试1: 访问 https://ipv4.webshare.io/")
        response = requests.get(
            "https://ipv4.webshare.io/",
            proxies=proxies,
            timeout=20
        )
        
        if response.status_code == 200:
            print(f"✅ 成功! 响应: {response.text[:100]}...")
            return True
        else:
            print(f"❌ 失败 - 状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 异常: {e}")
    
    try:
        # 测试2: 使用httpbin
        print("\n测试2: 访问 http://httpbin.org/ip")
        response = requests.get(
            "http://httpbin.org/ip",  # 使用HTTP而不是HTTPS
            proxies=proxies,
            timeout=20
        )
        
        if response.status_code == 200:
            print(f"✅ 成功! IP信息: {response.text}")
            return True
        else:
            print(f"❌ 失败 - 状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 异常: {e}")
    
    return False

def test_different_endpoints():
    """测试不同的Webshare端点"""
    print("\n🔍 测试不同的Webshare端点...")
    
    endpoints = [
        "p.webshare.io:80",
        "rotating-residential.webshare.io:80", 
        "proxy.webshare.io:80",
        "p.webshare.io:8080"
    ]
    
    for endpoint in endpoints:
        print(f"\n测试端点: {endpoint}")
        proxy_url = f"http://gorkpiln-1:6lwffvf16jjn@{endpoint}/"
        
        try:
            response = requests.get(
                "http://httpbin.org/ip",
                proxies={"http": proxy_url, "https": proxy_url},
                timeout=10
            )
            
            if response.status_code == 200:
                print(f"✅ {endpoint} 工作正常!")
                print(f"   IP: {response.json().get('origin')}")
                return endpoint
            else:
                print(f"❌ {endpoint} 失败 - 状态码: {response.status_code}")
                
        except Exception as e:
            print(f"❌ {endpoint} 异常: {str(e)[:100]}...")
    
    return None

if __name__ == '__main__':
    print("Webshare代理简单测试")
    print("=" * 40)
    
    # 测试基本连接
    success = test_webshare_proxy()
    
    if not success:
        # 如果失败，测试不同端点
        working_endpoint = test_different_endpoints()
        
        if working_endpoint:
            print(f"\n✅ 找到可用端点: {working_endpoint}")
        else:
            print("\n❌ 所有端点都失败了")
            print("\n可能的原因:")
            print("1. 代理账户需要激活")
            print("2. IP白名单限制")
            print("3. 代理服务器维护")
            print("\n建议:")
            print("1. 登录Webshare控制台检查状态")
            print("2. 确认套餐是否已激活")
            print("3. 检查账户余额")
    else:
        print("\n🎉 代理测试成功!")
        print("可以开始配置爬虫了")
