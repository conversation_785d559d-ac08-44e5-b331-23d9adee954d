#!/usr/bin/env python3
"""
无代理配置文件 - 用于调试和开发
当代理连接有问题时使用此配置

作者: GreenJoson
创建时间: 2025-06-26
"""

import os
from pathlib import Path

# 基础配置
BASE_DIR = Path(__file__).parent
DATA_DIR = BASE_DIR / "data"
LOG_DIR = BASE_DIR / "logs"

# 确保目录存在
DATA_DIR.mkdir(exist_ok=True)
LOG_DIR.mkdir(exist_ok=True)

# 数据库配置
DATABASE_CONFIG = {
    'path': str(DATA_DIR / 'youtube_channels.db'),
    'backup_interval': 100,  # 每100条记录备份一次
}

# 代理配置 - 禁用代理
PROXY_CONFIG = {
    'enabled': False,  # 禁用代理
    'proxy_file': str(BASE_DIR / 'proxy_list.txt'),
    'rotation_interval': 10,  # 每10个请求轮换代理
    'timeout': 20,
    'max_retries': 3,
    'retry_delay': 2,
}

# 爬虫配置 - 针对无代理优化
CRAWLER_CONFIG = {
    'max_channels_per_run': 20,  # 减少并发，避免被限制
    'request_delay_min': 3,      # 增加延迟
    'request_delay_max': 6,
    'concurrent_requests': 1,    # 单线程，避免触发反爬
    'user_agents': [
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    ],
    'headers': {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
}

# YouTube配置
YOUTUBE_CONFIG = {
    'base_url': 'https://www.youtube.com',
    'search_url': 'https://www.youtube.com/results',
    'channel_url_pattern': 'https://www.youtube.com/channel/{channel_id}',
    'handle_url_pattern': 'https://www.youtube.com/@{handle}',
    'api_key': None,  # 不使用API，直接爬取
}

# Twitter配置
TWITTER_CONFIG = {
    'base_url': 'https://twitter.com',
    'search_url': 'https://twitter.com/search',
    'enabled': False,  # 暂时禁用Twitter爬取
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': str(LOG_DIR / 'crawler.log'),
    'max_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5,
}

# 输出配置
OUTPUT_CONFIG = {
    'formats': ['json', 'csv'],
    'json_file': str(DATA_DIR / 'channels.json'),
    'csv_file': str(DATA_DIR / 'channels.csv'),
    'excel_file': str(DATA_DIR / 'channels.xlsx'),
}

# 测试配置
TEST_CONFIG = {
    'test_channels': [
        'UC_x5XG1OV2P6uZZ5FSM9Ttw',  # Google Developers
        'UCsooa4yRKGN_zEE8iknghZA',  # TED-Ed
        'UC-9-kyTW8ZkZNDHQJ6FgpwQ',  # Music
    ],
    'test_keywords': [
        'python programming',
        'web development',
        'artificial intelligence'
    ]
}

def get_config():
    """
    获取当前配置
    
    Returns:
        dict: 完整的配置字典
    """
    return {
        'database': DATABASE_CONFIG,
        'proxy': PROXY_CONFIG,
        'crawler': CRAWLER_CONFIG,
        'youtube': YOUTUBE_CONFIG,
        'twitter': TWITTER_CONFIG,
        'logging': LOGGING_CONFIG,
        'output': OUTPUT_CONFIG,
        'test': TEST_CONFIG,
        'base_dir': str(BASE_DIR),
        'data_dir': str(DATA_DIR),
        'log_dir': str(LOG_DIR),
    }

def print_config():
    """打印当前配置"""
    config = get_config()
    
    print("🔧 当前配置 (无代理模式)")
    print("=" * 50)
    print(f"📁 基础目录: {config['base_dir']}")
    print(f"📊 数据目录: {config['data_dir']}")
    print(f"📝 日志目录: {config['log_dir']}")
    print()
    print("🌐 代理配置:")
    print(f"  启用状态: {config['proxy']['enabled']}")
    print(f"  说明: 无代理模式，直接连接")
    print()
    print("🕷️ 爬虫配置:")
    print(f"  每次运行频道数: {config['crawler']['max_channels_per_run']}")
    print(f"  请求延迟: {config['crawler']['request_delay_min']}-{config['crawler']['request_delay_max']}秒")
    print(f"  并发请求数: {config['crawler']['concurrent_requests']}")
    print()
    print("⚠️  注意事项:")
    print("  - 无代理模式请求频率较低，避免被限制")
    print("  - 建议在代理问题解决后切换回代理模式")
    print("  - 适合开发和调试使用")

if __name__ == '__main__':
    print_config()
