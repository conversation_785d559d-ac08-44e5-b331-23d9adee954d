"""
数据库操作模块
负责SQLite数据库的创建、查询、插入和更新操作
"""

import sqlite3
import json
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from loguru import logger
from config import DATABASE_CONFIG


class DatabaseManager:
    """数据库管理器类，处理所有数据库相关操作"""

    def __init__(self, db_path: str = None):
        """
        初始化数据库管理器

        Args:
            db_path: 数据库文件路径，默认使用配置文件中的路径
        """
        self.db_path = db_path or DATABASE_CONFIG['db_path']
        self.init_database()
        logger.info(f"数据库管理器初始化完成，数据库路径: {self.db_path}")

    def init_database(self):
        """初始化数据库，创建必要的表结构"""
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.cursor()

                # 创建频道信息表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS channels (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        channel_id TEXT UNIQUE NOT NULL,
                        channel_url TEXT NOT NULL,
                        channel_name TEXT,
                        subscriber_count INTEGER,
                        video_count INTEGER,
                        description TEXT,
                        twitter_links TEXT,  -- JSON格式存储多个Twitter链接
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        last_crawled TIMESTAMP,
                        crawl_status TEXT DEFAULT 'pending',  -- pending, success, failed, skipped
                        error_message TEXT
                    )
                ''')

                # 创建爬取日志表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS crawl_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        channel_id TEXT,
                        action TEXT,  -- discover, crawl, update
                        status TEXT,  -- success, failed
                        message TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (channel_id) REFERENCES channels (channel_id)
                    )
                ''')

                # 创建代理状态表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS proxy_status (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        proxy_url TEXT UNIQUE NOT NULL,
                        is_active BOOLEAN DEFAULT 1,
                        failure_count INTEGER DEFAULT 0,
                        last_used TIMESTAMP,
                        last_test TIMESTAMP,
                        response_time REAL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # 创建索引提高查询性能
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_channel_id ON channels(channel_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_crawl_status ON channels(crawl_status)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_twitter_links ON channels(twitter_links)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_last_crawled ON channels(last_crawled)')

                conn.commit()
                logger.info("数据库表结构初始化完成")

        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise

    def insert_channel(self, channel_data: Dict) -> bool:
        """
        插入新的频道数据

        Args:
            channel_data: 频道数据字典

        Returns:
            bool: 插入是否成功
        """
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.cursor()

                # 准备插入数据
                twitter_links_json = json.dumps(channel_data.get('twitter_links', []))

                cursor.execute('''
                    INSERT OR REPLACE INTO channels
                    (channel_id, channel_url, channel_name, subscriber_count,
                     video_count, description, twitter_links, last_crawled,
                     crawl_status, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    channel_data.get('channel_id'),
                    channel_data.get('channel_url'),
                    channel_data.get('channel_name'),
                    channel_data.get('subscriber_count'),
                    channel_data.get('video_count'),
                    channel_data.get('description'),
                    twitter_links_json,
                    datetime.now(),
                    'success',
                    datetime.now()
                ))

                conn.commit()
                logger.info(f"频道数据插入成功: {channel_data.get('channel_name')}")
                return True

        except Exception as e:
            logger.error(f"插入频道数据失败: {e}")
            return False

    def get_channel_by_id(self, channel_id: str) -> Optional[Dict]:
        """
        根据频道ID获取频道信息

        Args:
            channel_id: YouTube频道ID

        Returns:
            Dict: 频道信息字典，如果不存在返回None
        """
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                cursor.execute('SELECT * FROM channels WHERE channel_id = ?', (channel_id,))
                row = cursor.fetchone()

                if row:
                    channel_data = dict(row)
                    # 解析JSON格式的Twitter链接
                    if channel_data['twitter_links']:
                        channel_data['twitter_links'] = json.loads(channel_data['twitter_links'])
                    return channel_data

                return None

        except Exception as e:
            logger.error(f"查询频道数据失败: {e}")
            return None

    def get_channels_with_twitter(self) -> List[Dict]:
        """
        获取所有包含Twitter链接的频道

        Returns:
            List[Dict]: 包含Twitter链接的频道列表
        """
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT * FROM channels
                    WHERE twitter_links IS NOT NULL
                    AND twitter_links != '[]'
                    AND twitter_links != ''
                    ORDER BY updated_at DESC
                ''')

                channels = []
                for row in cursor.fetchall():
                    channel_data = dict(row)
                    if channel_data['twitter_links']:
                        channel_data['twitter_links'] = json.loads(channel_data['twitter_links'])
                    channels.append(channel_data)

                logger.info(f"查询到 {len(channels)} 个包含Twitter链接的频道")
                return channels

        except Exception as e:
            logger.error(f"查询Twitter频道失败: {e}")
            return []

    def get_pending_channels(self, limit: int = 100) -> List[str]:
        """
        获取待爬取的频道ID列表

        Args:
            limit: 返回的最大数量

        Returns:
            List[str]: 待爬取的频道ID列表
        """
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT channel_id FROM channels
                    WHERE crawl_status = 'pending'
                    OR (crawl_status = 'failed' AND
                        (last_crawled IS NULL OR
                         datetime(last_crawled) < datetime('now', '-1 day')))
                    ORDER BY created_at ASC
                    LIMIT ?
                ''', (limit,))

                channel_ids = [row[0] for row in cursor.fetchall()]
                logger.info(f"获取到 {len(channel_ids)} 个待爬取频道")
                return channel_ids

        except Exception as e:
            logger.error(f"查询待爬取频道失败: {e}")
            return []

    def log_crawl_action(self, channel_id: str, action: str, status: str, message: str = None):
        """
        记录爬取操作日志

        Args:
            channel_id: 频道ID
            action: 操作类型
            status: 操作状态
            message: 详细信息
        """
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    INSERT INTO crawl_logs (channel_id, action, status, message)
                    VALUES (?, ?, ?, ?)
                ''', (channel_id, action, status, message))

                conn.commit()

        except Exception as e:
            logger.error(f"记录爬取日志失败: {e}")

    def get_statistics(self) -> Dict:
        """
        获取数据库统计信息

        Returns:
            Dict: 统计信息字典
        """
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.cursor()

                # 总频道数
                cursor.execute('SELECT COUNT(*) FROM channels')
                total_channels = cursor.fetchone()[0]

                # 包含Twitter的频道数
                cursor.execute('''
                    SELECT COUNT(*) FROM channels
                    WHERE twitter_links IS NOT NULL
                    AND twitter_links != '[]'
                    AND twitter_links != ''
                ''')
                twitter_channels = cursor.fetchone()[0]

                # 各状态频道数
                cursor.execute('SELECT crawl_status, COUNT(*) FROM channels GROUP BY crawl_status')
                status_counts = dict(cursor.fetchall())

                # 最近爬取时间
                cursor.execute('SELECT MAX(last_crawled) FROM channels')
                last_crawl_time = cursor.fetchone()[0]

                stats = {
                    'total_channels': total_channels,
                    'twitter_channels': twitter_channels,
                    'twitter_ratio': twitter_channels / total_channels if total_channels > 0 else 0,
                    'status_counts': status_counts,
                    'last_crawl_time': last_crawl_time,
                }

                logger.info(f"数据库统计: {stats}")
                return stats

        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}


if __name__ == '__main__':
    # 测试数据库功能
    db = DatabaseManager()
    stats = db.get_statistics()
    print(f"数据库统计信息: {stats}")
