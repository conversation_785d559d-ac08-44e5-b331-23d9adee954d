#!/usr/bin/env python3
"""
使用已知频道测试爬虫功能
绕过搜索问题，直接测试频道爬取

作者: GreenJoson
创建时间: 2025-06-26
"""

import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from database import DatabaseManager
from spider import YouTubeSpider
from loguru import logger

def test_with_known_channels():
    """使用已知频道测试"""
    print("🧪 使用已知频道测试爬虫")
    print("=" * 50)
    
    # 知名YouTube频道列表
    test_channels = [
        "https://www.youtube.com/@MrBeast",
        "https://www.youtube.com/@PewDiePie", 
        "https://www.youtube.com/@Markiplier",
        "https://www.youtube.com/@GameGrumps",
        "https://www.youtube.com/@LinusTechTips",
        "https://www.youtube.com/@MKBHD",
        "https://www.youtube.com/@UnboxTherapy",
        "https://www.youtube.com/@TechLinked",
        "https://www.youtube.com/@veritasium",
        "https://www.youtube.com/@3Blue1Brown"
    ]
    
    # 初始化组件
    db = DatabaseManager()
    spider = YouTubeSpider()
    
    print(f"准备测试 {len(test_channels)} 个频道")
    
    # 添加频道到数据库
    added_count = 0
    for channel_url in test_channels:
        try:
            # 检查是否已存在
            existing = db.get_channel_by_url(channel_url)
            if not existing:
                # 添加新频道
                channel_id = db.add_channel(channel_url, 'pending')
                if channel_id:
                    added_count += 1
                    print(f"  ✅ 添加频道: {channel_url}")
                else:
                    print(f"  ❌ 添加失败: {channel_url}")
            else:
                print(f"  ⚠️  频道已存在: {channel_url}")
                
        except Exception as e:
            print(f"  ❌ 处理异常: {channel_url}, 错误: {e}")
    
    print(f"\n成功添加 {added_count} 个新频道到数据库")
    
    # 获取待爬取频道
    pending_channels = db.get_pending_channels(limit=5)  # 只测试前5个
    print(f"获取到 {len(pending_channels)} 个待爬取频道")
    
    # 爬取频道
    crawled_count = 0
    twitter_found = 0
    
    for channel in pending_channels:
        try:
            print(f"\n🎯 爬取频道: {channel['url']}")
            
            # 爬取频道信息
            channel_info = spider.crawl_channel(channel['url'])
            
            if channel_info:
                print(f"  ✅ 爬取成功")
                print(f"     标题: {channel_info.get('title', 'N/A')}")
                print(f"     描述长度: {len(channel_info.get('description', ''))}")
                print(f"     Twitter链接: {len(channel_info.get('twitter_links', []))}")
                
                # 更新数据库
                db.update_channel_info(
                    channel['id'],
                    channel_info['title'],
                    channel_info['description'],
                    channel_info['twitter_links'],
                    'completed'
                )
                
                crawled_count += 1
                if channel_info.get('twitter_links'):
                    twitter_found += 1
                    print(f"     🐦 Twitter链接: {channel_info['twitter_links']}")
                
            else:
                print(f"  ❌ 爬取失败")
                db.update_channel_status(channel['id'], 'failed')
                
        except Exception as e:
            print(f"  ❌ 爬取异常: {e}")
            db.update_channel_status(channel['id'], 'failed')
    
    # 统计结果
    print(f"\n" + "=" * 50)
    print(f"📊 测试结果:")
    print(f"  总测试频道: {len(pending_channels)}")
    print(f"  成功爬取: {crawled_count}")
    print(f"  发现Twitter: {twitter_found}")
    print(f"  Twitter发现率: {twitter_found/crawled_count*100:.1f}%" if crawled_count > 0 else "  Twitter发现率: 0%")
    
    # 显示数据库统计
    stats = db.get_statistics()
    print(f"\n📈 数据库统计:")
    print(f"  总频道数: {stats['total_channels']}")
    print(f"  Twitter频道数: {stats['twitter_channels']}")
    print(f"  整体Twitter发现率: {stats['twitter_ratio']:.1f}%")
    
    return crawled_count > 0

def main():
    """主函数"""
    print("🚀 YouTube爬虫功能测试")
    print("使用已知频道绕过搜索问题")
    print()
    
    success = test_with_known_channels()
    
    if success:
        print("\n🎉 爬虫核心功能正常!")
        print("💡 下一步:")
        print("1. 修复搜索功能（使用Selenium）")
        print("2. 或者使用预定义频道列表")
        print("3. 优化代理配置")
        print("4. 部署到生产环境")
    else:
        print("\n❌ 爬虫功能有问题")
        print("💡 需要进一步调试")

if __name__ == '__main__':
    main()
