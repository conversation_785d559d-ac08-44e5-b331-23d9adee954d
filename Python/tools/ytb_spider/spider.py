"""
YouTube爬虫核心模块
负责频道发现、数据提取和Twitter链接识别
"""

import re
import time
import random
from typing import List, Dict, Optional, Set
from urllib.parse import urljoin, urlparse
import requests
from bs4 import BeautifulSoup
from loguru import logger

from config import YOUTUBE_CONFIG, SPIDER_CONFIG
from database import DatabaseManager
from proxy_manager import ProxyManager
from utils import twitter_extractor, rate_limiter, TextProcessor, URLValidator


class YouTubeSpider:
    """YouTube爬虫主类，负责频道发现和数据提取"""

    def __init__(self):
        """初始化YouTube爬虫"""
        self.db = DatabaseManager()
        self.proxy_manager = ProxyManager()
        self.text_processor = TextProcessor()
        self.discovered_channels = set()
        self.session = requests.Session()

        logger.info("YouTube爬虫初始化完成")

    def discover_channels(self, search_keywords: List[str] = None, max_channels: int = 50) -> List[str]:
        """
        发现新的YouTube频道

        Args:
            search_keywords: 搜索关键词列表
            max_channels: 最大发现频道数

        Returns:
            List[str]: 发现的频道URL列表
        """
        if not search_keywords:
            search_keywords = YOUTUBE_CONFIG['search_keywords']

        discovered_urls = set()

        for keyword in search_keywords[:5]:  # 限制关键词数量
            try:
                logger.info(f"搜索关键词: {keyword}")
                urls = self._search_channels_by_keyword(keyword, max_channels // len(search_keywords))
                discovered_urls.update(urls)

                if len(discovered_urls) >= max_channels:
                    break

                # 请求间隔
                rate_limiter.wait()

            except Exception as e:
                logger.error(f"搜索频道失败，关键词: {keyword}, 错误: {e}")
                continue

        result = list(discovered_urls)[:max_channels]
        logger.info(f"总共发现 {len(result)} 个频道")
        return result

    def _search_channels_by_keyword(self, keyword: str, max_results: int = 20) -> List[str]:
        """
        根据关键词搜索频道

        Args:
            keyword: 搜索关键词
            max_results: 最大结果数

        Returns:
            List[str]: 频道URL列表
        """
        search_url = f"{YOUTUBE_CONFIG['base_url']}/results?search_query={keyword}&sp=EgIQAg%253D%253D"  # 只搜索频道

        try:
            response = self.proxy_manager.make_request(search_url)
            if not response:
                logger.warning(f"搜索请求失败: {keyword}")
                return []

            soup = BeautifulSoup(response.text, 'html.parser')
            channel_urls = self._extract_channel_urls_from_search(soup)

            logger.info(f"关键词 '{keyword}' 搜索到 {len(channel_urls)} 个频道")
            return channel_urls[:max_results]

        except Exception as e:
            logger.error(f"搜索频道异常: {keyword}, 错误: {e}")
            return []

    def _extract_channel_urls_from_search(self, soup: BeautifulSoup) -> List[str]:
        """
        从搜索结果页面提取频道URL

        Args:
            soup: BeautifulSoup对象

        Returns:
            List[str]: 频道URL列表
        """
        channel_urls = set()

        # 查找频道链接
        for link in soup.find_all('a', href=True):
            href = link['href']

            # 匹配频道URL模式
            for pattern in YOUTUBE_CONFIG['channel_url_patterns']:
                if re.match(pattern.replace('\\', ''), href):
                    full_url = urljoin(YOUTUBE_CONFIG['base_url'], href)
                    if URLValidator.is_valid_youtube_url(full_url):
                        channel_urls.add(full_url)

        return list(channel_urls)

    def crawl_channel(self, channel_url: str) -> Optional[Dict]:
        """
        爬取单个频道的详细信息

        Args:
            channel_url: 频道URL

        Returns:
            Dict: 频道信息字典，失败返回None
        """
        try:
            logger.info(f"开始爬取频道: {channel_url}")

            # 检查是否已经爬取过
            channel_id = URLValidator.extract_channel_id(channel_url)
            if channel_id:
                existing_channel = self.db.get_channel_by_id(channel_id)
                if existing_channel and existing_channel.get('crawl_status') == 'success':
                    logger.info(f"频道已存在，跳过: {channel_id}")
                    return existing_channel

            # 请求频道页面
            response = self.proxy_manager.make_request(channel_url)
            if not response:
                logger.warning(f"频道页面请求失败: {channel_url}")
                return None

            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取频道信息
            channel_data = self._extract_channel_info(soup, channel_url)

            if channel_data:
                # 保存到数据库
                success = self.db.insert_channel(channel_data)
                if success:
                    # 创建Twitter映射关系
                    self._create_twitter_mappings(channel_data)

                    self.db.log_crawl_action(
                        channel_data.get('channel_id', ''),
                        'crawl',
                        'success',
                        f"成功爬取频道: {channel_data.get('channel_name')}"
                    )

                    # 显示发现的Twitter链接
                    twitter_links = channel_data.get('twitter_links', [])
                    if twitter_links:
                        logger.info(f"频道爬取成功: {channel_data.get('channel_name')}")
                        logger.info(f"🐦 发现 {len(twitter_links)} 个Twitter链接:")
                        for i, link in enumerate(twitter_links, 1):
                            logger.info(f"   {i}. {link}")
                    else:
                        logger.info(f"频道爬取成功: {channel_data.get('channel_name')} (未发现Twitter链接)")

                    return channel_data
                else:
                    logger.error(f"频道数据保存失败: {channel_url}")

            return None

        except Exception as e:
            logger.error(f"爬取频道异常: {channel_url}, 错误: {e}")
            if channel_id:
                self.db.log_crawl_action(channel_id, 'crawl', 'failed', str(e))
            return None

    def _extract_channel_info(self, soup: BeautifulSoup, channel_url: str) -> Optional[Dict]:
        """
        从频道页面提取信息

        Args:
            soup: BeautifulSoup对象
            channel_url: 频道URL

        Returns:
            Dict: 频道信息字典
        """
        try:
            channel_data = {
                'channel_url': channel_url,
                'channel_id': URLValidator.extract_channel_id(channel_url),
                'twitter_links': [],
            }

            # 提取频道名称
            name_selectors = [
                'meta[property="og:title"]',
                'title',
                '[class*="channel-name"]',
                'h1'
            ]

            for selector in name_selectors:
                element = soup.select_one(selector)
                if element:
                    if element.name == 'meta':
                        channel_data['channel_name'] = element.get('content', '').strip()
                    else:
                        channel_data['channel_name'] = element.get_text().strip()
                    break

            # 提取频道描述
            desc_selectors = [
                'meta[property="og:description"]',
                'meta[name="description"]',
                '[class*="description"]',
                '[class*="about"]'
            ]

            description_text = ""
            for selector in desc_selectors:
                element = soup.select_one(selector)
                if element:
                    if element.name == 'meta':
                        description_text = element.get('content', '').strip()
                    else:
                        description_text = element.get_text().strip()
                    break

            channel_data['description'] = self.text_processor.clean_text(description_text)

            # 提取订阅者数量
            subscriber_text = self._find_text_by_patterns(soup, [
                r'(\d+(?:\.\d+)?[KMB]?)\s*subscribers?',
                r'(\d+(?:,\d+)*)\s*subscribers?'
            ])

            if subscriber_text:
                channel_data['subscriber_count'] = self.text_processor.extract_numbers(subscriber_text)

            # 提取视频数量
            video_text = self._find_text_by_patterns(soup, [
                r'(\d+(?:\.\d+)?[KMB]?)\s*videos?',
                r'(\d+(?:,\d+)*)\s*videos?'
            ])

            if video_text:
                channel_data['video_count'] = self.text_processor.extract_numbers(video_text)

            # 提取Twitter链接
            page_html = str(soup)
            twitter_links = twitter_extractor.extract_from_html(page_html)
            channel_data['twitter_links'] = twitter_links

            # 尝试访问About页面获取更多信息
            about_url = self._get_about_page_url(channel_url)
            if about_url:
                about_info = self._crawl_about_page(about_url)
                if about_info:
                    # 合并About页面的信息
                    if about_info.get('description') and len(about_info['description']) > len(channel_data.get('description', '')):
                        channel_data['description'] = about_info['description']

                    if about_info.get('twitter_links'):
                        all_twitter_links = set(channel_data['twitter_links'])
                        all_twitter_links.update(about_info['twitter_links'])
                        channel_data['twitter_links'] = list(all_twitter_links)

            logger.debug(f"提取的频道信息: {channel_data}")
            return channel_data

        except Exception as e:
            logger.error(f"提取频道信息失败: {e}")
            return None

    def _find_text_by_patterns(self, soup: BeautifulSoup, patterns: List[str]) -> Optional[str]:
        """
        根据正则表达式模式查找文本

        Args:
            soup: BeautifulSoup对象
            patterns: 正则表达式模式列表

        Returns:
            str: 匹配的文本，未找到返回None
        """
        page_text = soup.get_text()

        for pattern in patterns:
            match = re.search(pattern, page_text, re.IGNORECASE)
            if match:
                return match.group(0)

        return None

    def _create_twitter_mappings(self, channel_data: Dict) -> None:
        """
        为频道创建Twitter映射关系

        Args:
            channel_data: 频道数据字典
        """
        try:
            channel_id = channel_data.get('channel_id')
            channel_url = channel_data.get('channel_url')
            twitter_links = channel_data.get('twitter_links', [])

            if not channel_id or not channel_url or not twitter_links:
                return

            # 为每个Twitter链接创建映射
            for twitter_url in twitter_links:
                success = self.db.add_twitter_mapping(channel_id, channel_url, twitter_url)
                if success:
                    logger.debug(f"创建Twitter映射: {channel_url} -> {twitter_url}")
                else:
                    logger.warning(f"创建Twitter映射失败: {channel_url} -> {twitter_url}")

        except Exception as e:
            logger.error(f"创建Twitter映射异常: {e}")

    def _get_about_page_url(self, channel_url: str) -> Optional[str]:
        """
        获取频道About页面URL

        Args:
            channel_url: 频道主页URL

        Returns:
            str: About页面URL
        """
        try:
            if channel_url.endswith('/'):
                return channel_url + 'about'
            else:
                return channel_url + '/about'
        except Exception:
            return None

    def _crawl_about_page(self, about_url: str) -> Optional[Dict]:
        """
        爬取频道About页面

        Args:
            about_url: About页面URL

        Returns:
            Dict: About页面信息
        """
        try:
            rate_limiter.wait()  # 请求间隔

            response = self.proxy_manager.make_request(about_url)
            if not response:
                return None

            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取描述信息
            description = ""
            desc_elements = soup.find_all(['p', 'div'], string=re.compile(r'.{20,}'))
            if desc_elements:
                description = desc_elements[0].get_text().strip()

            # 提取Twitter链接
            twitter_links = twitter_extractor.extract_from_html(str(soup))

            return {
                'description': self.text_processor.clean_text(description),
                'twitter_links': twitter_links,
            }

        except Exception as e:
            logger.debug(f"爬取About页面失败: {about_url}, 错误: {e}")
            return None


if __name__ == '__main__':
    # 测试爬虫功能
    spider = YouTubeSpider()

    # 测试频道发现
    channels = spider.discover_channels(['gaming'], max_channels=5)
    print(f"发现的频道: {channels}")

    # 测试频道爬取
    if channels:
        channel_data = spider.crawl_channel(channels[0])
        print(f"爬取的频道数据: {channel_data}")
