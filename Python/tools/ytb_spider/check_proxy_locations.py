#!/usr/bin/env python3
"""
检查Webshare代理的地理位置分布和配置选项
分析可用的国家/地区，以及是否可以指定特定地区

作者: GreenJoson
创建时间: 2025-06-26
"""

import requests
import json
import time
from collections import Counter

class WebshareLocationChecker:
    """Webshare代理地理位置检查器"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://proxy.webshare.io/api/v2"
        self.headers = {
            "Authorization": f"Token {api_key}",
            "Content-Type": "application/json"
        }
    
    def get_proxy_config(self):
        """获取代理配置，包含地理位置信息"""
        try:
            response = requests.get(
                f"{self.base_url}/proxy/config/",
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                return {"success": True, "data": response.json()}
            else:
                return {"success": False, "error": f"HTTP {response.status_code}"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def analyze_country_distribution(self, countries_data):
        """分析国家分布"""
        print("🌍 全球IP分布分析:")
        print("=" * 60)
        
        # 按地区分类
        regions = {
            "亚太地区": ["CN", "JP", "KR", "TW", "HK", "SG", "MY", "TH", "VN", "PH", "ID", "IN", "AU", "NZ"],
            "北美地区": ["US", "CA", "MX"],
            "欧洲地区": ["GB", "DE", "FR", "IT", "ES", "NL", "RU", "PL", "TR", "UA"],
            "南美地区": ["BR", "AR", "CL", "CO", "PE", "VE"],
            "中东非洲": ["SA", "AE", "IL", "ZA", "EG", "MA", "NG"]
        }
        
        region_totals = {}
        
        for region, country_codes in regions.items():
            total = sum(countries_data.get(code, 0) for code in country_codes)
            region_totals[region] = total
            
            print(f"\n📍 {region}: {total:,} 个IP")
            
            # 显示该地区前5个国家
            region_countries = [(code, countries_data.get(code, 0)) 
                              for code in country_codes if countries_data.get(code, 0) > 0]
            region_countries.sort(key=lambda x: x[1], reverse=True)
            
            for code, count in region_countries[:5]:
                country_name = self.get_country_name(code)
                print(f"  {country_name} ({code}): {count:,}")
        
        # 总计
        total_ips = sum(countries_data.values())
        print(f"\n📊 总计: {total_ips:,} 个IP")
        
        # 地区占比
        print(f"\n📈 地区占比:")
        for region, count in sorted(region_totals.items(), key=lambda x: x[1], reverse=True):
            percentage = (count / total_ips) * 100
            print(f"  {region}: {percentage:.1f}%")
        
        return region_totals
    
    def get_country_name(self, code):
        """获取国家名称"""
        country_names = {
            "US": "美国", "CN": "中国", "DE": "德国", "GB": "英国", "FR": "法国",
            "IT": "意大利", "ES": "西班牙", "BR": "巴西", "MX": "墨西哥", "CA": "加拿大",
            "JP": "日本", "KR": "韩国", "TW": "台湾", "AU": "澳大利亚", "IN": "印度",
            "RU": "俄罗斯", "TR": "土耳其", "SA": "沙特", "AE": "阿联酋", "NL": "荷兰",
            "PL": "波兰", "UA": "乌克兰", "AR": "阿根廷", "CL": "智利", "CO": "哥伦比亚",
            "PE": "秘鲁", "VE": "委内瑞拉", "MY": "马来西亚", "TH": "泰国", "VN": "越南",
            "PH": "菲律宾", "ID": "印尼", "SG": "新加坡", "HK": "香港", "ZA": "南非",
            "EG": "埃及", "MA": "摩洛哥", "NG": "尼日利亚", "IL": "以色列", "NZ": "新西兰"
        }
        return country_names.get(code, code)
    
    def check_location_control_options(self):
        """检查是否可以控制代理位置"""
        print("\n🎯 代理位置控制选项:")
        print("=" * 40)
        
        # 检查是否可以设置国家限制
        try:
            # 尝试获取可用的配置选项
            response = requests.get(
                f"{self.base_url}/proxy/config/",
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                config = response.json()
                
                # 检查配置选项
                print("📋 当前配置:")
                print(f"  IP授权国家代码: {config.get('ip_authorization_country_codes', '无限制')}")
                print(f"  自动替换无效代理: {config.get('auto_replace_invalid_proxies', False)}")
                print(f"  自动替换低置信度代理: {config.get('auto_replace_low_country_confidence_proxies', False)}")
                
                # 检查可用国家
                available_countries = config.get('available_countries', {})
                if available_countries:
                    print(f"  可指定国家数: {len(available_countries)}")
                else:
                    print("  ✅ 可使用所有国家的IP")
                
                return config
            else:
                print(f"❌ 无法获取配置: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 检查配置失败: {e}")
            return None
    
    def test_location_rotation(self):
        """测试IP位置轮换"""
        print("\n🔄 测试IP位置轮换:")
        print("=" * 40)
        
        username = "gorkpiln"
        password = "6lwffvf16jjn"
        proxy_url = f"http://{username}:{password}@p.webshare.io:80"
        
        proxies = {
            "http": proxy_url,
            "https": proxy_url
        }
        
        locations = []
        
        print("发送5个请求测试IP轮换...")
        
        for i in range(5):
            try:
                # 获取IP信息
                response = requests.get(
                    "http://ip-api.com/json/",
                    proxies=proxies,
                    timeout=15,
                    headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    location_info = {
                        "ip": data.get("query"),
                        "country": data.get("country"),
                        "city": data.get("city"),
                        "region": data.get("regionName"),
                        "countryCode": data.get("countryCode")
                    }
                    locations.append(location_info)
                    
                    print(f"  请求 {i+1}: {location_info['ip']} - {location_info['city']}, {location_info['country']}")
                else:
                    print(f"  请求 {i+1}: 失败 - 状态码 {response.status_code}")
                    
            except Exception as e:
                print(f"  请求 {i+1}: 异常 - {str(e)[:50]}...")
            
            time.sleep(3)  # 等待IP轮换
        
        # 分析轮换结果
        if locations:
            unique_ips = len(set(loc["ip"] for loc in locations))
            unique_countries = len(set(loc["country"] for loc in locations))
            
            print(f"\n📊 轮换分析:")
            print(f"  总请求数: {len(locations)}")
            print(f"  不同IP数: {unique_ips}")
            print(f"  不同国家数: {unique_countries}")
            
            if unique_ips > 1:
                print("  ✅ IP确实在轮换")
            else:
                print("  ⚠️  IP没有轮换（可能需要更长间隔）")
            
            # 显示国家分布
            countries = [loc["country"] for loc in locations if loc["country"]]
            country_count = Counter(countries)
            
            print(f"\n🌍 本次测试的国家分布:")
            for country, count in country_count.items():
                print(f"  {country}: {count} 次")
        
        return locations

def main():
    """主函数"""
    print("🌍 Webshare代理地理位置分析工具")
    print("=" * 60)
    
    api_key = "ulul4v9sr44nvbd2a0v3yhyzpzs2dfgvkdcsnoyg"
    checker = WebshareLocationChecker(api_key)
    
    # 1. 获取代理配置
    print("📡 获取代理配置...")
    config_result = checker.get_proxy_config()
    
    if config_result["success"]:
        config_data = config_result["data"]
        countries_data = config_data.get("countries", {})
        
        if countries_data:
            # 2. 分析国家分布
            region_totals = checker.analyze_country_distribution(countries_data)
            
            # 3. 检查位置控制选项
            checker.check_location_control_options()
            
            # 4. 测试IP轮换（如果代理可用）
            print("\n" + "=" * 60)
            print("💡 关于地理位置控制:")
            print("1. Rotating Residential 代理会自动轮换全球IP")
            print("2. 默认情况下无法指定特定国家/地区")
            print("3. 如需指定地区，可能需要升级到更高级套餐")
            print("4. 可以通过API设置 ip_authorization_country_codes 限制国家")
            
            print(f"\n📝 您的代理包含:")
            for region, count in sorted(region_totals.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / sum(region_totals.values())) * 100
                print(f"  {region}: {count:,} IP ({percentage:.1f}%)")
            
            print(f"\n🎯 推荐配置:")
            print("- 如果主要爬取中文内容：亚太地区IP较多")
            print("- 如果爬取国际内容：美国IP最多，欧洲次之")
            print("- 当前配置支持全球轮换，覆盖面广")
            
        else:
            print("❌ 未找到国家分布数据")
    else:
        print(f"❌ 获取配置失败: {config_result['error']}")

if __name__ == '__main__':
    main()
