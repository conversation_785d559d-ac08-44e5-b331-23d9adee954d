"""
工具函数模块
包含各种辅助功能，如Twitter链接提取、文本处理等
"""

import re
import time
import random
from typing import List, Set, Optional, Dict
from urllib.parse import urlparse, urljoin
from loguru import logger
from config import TWITTER_CONFIG, SPIDER_CONFIG


class TwitterExtractor:
    """Twitter链接提取器类"""
    
    def __init__(self):
        """初始化Twitter链接提取器"""
        self.patterns = [re.compile(pattern, re.IGNORECASE) for pattern in TWITTER_CONFIG['patterns']]
        logger.debug("Twitter链接提取器初始化完成")
    
    def extract_twitter_links(self, text: str) -> List[str]:
        """
        从文本中提取Twitter/X链接
        
        Args:
            text: 待提取的文本内容
            
        Returns:
            List[str]: 提取到的Twitter链接列表
        """
        if not text:
            return []
        
        twitter_links = set()
        
        for pattern in self.patterns:
            matches = pattern.findall(text)
            for match in matches:
                if isinstance(match, tuple):
                    # 处理分组匹配
                    username = match[0] if match[0] else match[1] if len(match) > 1 else None
                else:
                    username = match
                
                if username and self.is_valid_twitter_username(username):
                    # 标准化链接格式
                    normalized_link = self.normalize_twitter_link(username)
                    if normalized_link:
                        twitter_links.add(normalized_link)
        
        result = list(twitter_links)
        if result:
            logger.info(f"提取到 {len(result)} 个Twitter链接: {result}")
        
        return result
    
    def is_valid_twitter_username(self, username: str) -> bool:
        """
        验证Twitter用户名是否有效
        
        Args:
            username: Twitter用户名
            
        Returns:
            bool: 是否有效
        """
        if not username:
            return False
        
        # 移除@符号
        username = username.lstrip('@')
        
        # Twitter用户名规则：1-15个字符，只能包含字母、数字、下划线
        if len(username) < 1 or len(username) > 15:
            return False
        
        if not re.match(r'^[a-zA-Z0-9_]+$', username):
            return False
        
        # 排除一些常见的无效用户名
        invalid_usernames = {
            'twitter', 'x', 'admin', 'support', 'help', 'api', 'www',
            'mail', 'email', 'contact', 'info', 'news', 'blog'
        }
        
        if username.lower() in invalid_usernames:
            return False
        
        return True
    
    def normalize_twitter_link(self, username: str) -> Optional[str]:
        """
        标准化Twitter链接格式
        
        Args:
            username: Twitter用户名
            
        Returns:
            str: 标准化的Twitter链接
        """
        if not username:
            return None
        
        # 移除@符号和空格
        username = username.lstrip('@').strip()
        
        if not self.is_valid_twitter_username(username):
            return None
        
        # 统一使用x.com域名
        return f"https://x.com/{username}"
    
    def extract_from_html(self, html_content: str) -> List[str]:
        """
        从HTML内容中提取Twitter链接
        
        Args:
            html_content: HTML内容
            
        Returns:
            List[str]: 提取到的Twitter链接列表
        """
        if not html_content:
            return []
        
        # 从href属性中提取
        href_pattern = r'href=["\']([^"\']*(?:twitter\.com|x\.com)[^"\']*)["\']'
        href_matches = re.findall(href_pattern, html_content, re.IGNORECASE)
        
        # 从文本内容中提取
        text_links = self.extract_twitter_links(html_content)
        
        # 合并并去重
        all_links = set(text_links)
        
        for href in href_matches:
            # 解析URL获取用户名
            try:
                parsed = urlparse(href)
                if parsed.netloc.lower() in ['twitter.com', 'x.com', 'www.twitter.com', 'www.x.com']:
                    path_parts = parsed.path.strip('/').split('/')
                    if path_parts and path_parts[0]:
                        username = path_parts[0]
                        normalized = self.normalize_twitter_link(username)
                        if normalized:
                            all_links.add(normalized)
            except Exception as e:
                logger.debug(f"解析Twitter链接失败: {href}, 错误: {e}")
        
        return list(all_links)


class TextProcessor:
    """文本处理工具类"""
    
    @staticmethod
    def clean_text(text: str) -> str:
        """
        清理文本内容
        
        Args:
            text: 原始文本
            
        Returns:
            str: 清理后的文本
        """
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        
        # 移除特殊字符
        text = re.sub(r'[^\w\s@#.,!?-]', '', text)
        
        return text.strip()
    
    @staticmethod
    def extract_numbers(text: str) -> Optional[int]:
        """
        从文本中提取数字（如订阅者数量）
        
        Args:
            text: 包含数字的文本
            
        Returns:
            int: 提取到的数字，失败返回None
        """
        if not text:
            return None
        
        # 移除逗号和空格
        text = re.sub(r'[,\s]', '', text)
        
        # 处理K、M、B等单位
        multipliers = {'K': 1000, 'M': 1000000, 'B': 1000000000}
        
        for unit, multiplier in multipliers.items():
            if unit in text.upper():
                number_part = re.search(r'([\d.]+)', text)
                if number_part:
                    try:
                        return int(float(number_part.group(1)) * multiplier)
                    except ValueError:
                        continue
        
        # 直接提取数字
        numbers = re.findall(r'\d+', text)
        if numbers:
            try:
                return int(numbers[0])
            except ValueError:
                pass
        
        return None


class RateLimiter:
    """请求频率限制器"""
    
    def __init__(self, min_delay: float = 2.0, max_delay: float = 5.0):
        """
        初始化频率限制器
        
        Args:
            min_delay: 最小延迟时间（秒）
            max_delay: 最大延迟时间（秒）
        """
        self.min_delay = min_delay
        self.max_delay = max_delay
        self.last_request_time = 0
        logger.debug(f"频率限制器初始化: {min_delay}-{max_delay}秒")
    
    def wait(self):
        """等待适当的时间间隔"""
        current_time = time.time()
        elapsed = current_time - self.last_request_time
        
        delay = random.uniform(self.min_delay, self.max_delay)
        
        if elapsed < delay:
            sleep_time = delay - elapsed
            logger.debug(f"频率限制等待: {sleep_time:.2f}秒")
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()


class URLValidator:
    """URL验证器"""
    
    @staticmethod
    def is_valid_youtube_url(url: str) -> bool:
        """
        验证是否为有效的YouTube URL
        
        Args:
            url: 待验证的URL
            
        Returns:
            bool: 是否有效
        """
        if not url:
            return False
        
        try:
            parsed = urlparse(url)
            return parsed.netloc.lower() in ['youtube.com', 'www.youtube.com', 'm.youtube.com']
        except Exception:
            return False
    
    @staticmethod
    def extract_channel_id(url: str) -> Optional[str]:
        """
        从YouTube URL中提取频道ID
        
        Args:
            url: YouTube URL
            
        Returns:
            str: 频道ID，失败返回None
        """
        if not url:
            return None
        
        try:
            parsed = urlparse(url)
            path = parsed.path.strip('/')
            
            # 处理不同的URL格式
            if path.startswith('channel/'):
                return path.split('/', 1)[1]
            elif path.startswith('c/'):
                return path.split('/', 1)[1]
            elif path.startswith('@'):
                return path
            elif path.startswith('user/'):
                return path.split('/', 1)[1]
            
            return None
            
        except Exception as e:
            logger.debug(f"提取频道ID失败: {url}, 错误: {e}")
            return None


# 全局实例
twitter_extractor = TwitterExtractor()
rate_limiter = RateLimiter(
    min_delay=SPIDER_CONFIG['request_delay'][0],
    max_delay=SPIDER_CONFIG['request_delay'][1]
)


if __name__ == '__main__':
    # 测试工具函数
    test_text = """
    Follow me on Twitter: @testuser
    Check out https://twitter.com/example
    Visit my X profile: https://x.com/myprofile
    Contact: <EMAIL>
    """
    
    extractor = TwitterExtractor()
    links = extractor.extract_twitter_links(test_text)
    print(f"提取到的Twitter链接: {links}")
    
    # 测试数字提取
    processor = TextProcessor()
    print(f"1.2M subscribers -> {processor.extract_numbers('1.2M subscribers')}")
    print(f"500K views -> {processor.extract_numbers('500K views')}")
    print(f"1,234,567 -> {processor.extract_numbers('1,234,567')}")
