#!/usr/bin/env python3
"""
配置切换工具
在数据中心代理和住宅代理配置之间切换

使用方法:
python switch_config.py residential  # 切换到住宅代理配置
python switch_config.py datacenter   # 切换到数据中心代理配置
python switch_config.py status       # 查看当前配置状态

作者: GreenJoson
创建时间: 2025-06-26
版本: v1.0
"""

import sys
import shutil
from pathlib import Path
from typing import Dict, Any


class ConfigSwitcher:
    """配置切换器"""
    
    def __init__(self):
        """初始化配置切换器"""
        self.project_root = Path(__file__).parent
        self.config_file = self.project_root / 'config.py'
        self.residential_config = self.project_root / 'config_residential.py'
        self.backup_dir = self.project_root / 'config_backups'
        
        # 确保备份目录存在
        self.backup_dir.mkdir(exist_ok=True)
    
    def backup_current_config(self) -> str:
        """备份当前配置文件"""
        if not self.config_file.exists():
            return ""
        
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = self.backup_dir / f"config_backup_{timestamp}.py"
        
        shutil.copy2(self.config_file, backup_file)
        return str(backup_file)
    
    def switch_to_residential(self) -> bool:
        """切换到住宅代理配置"""
        try:
            if not self.residential_config.exists():
                print(f"❌ 住宅代理配置文件不存在: {self.residential_config}")
                return False
            
            # 备份当前配置
            backup_file = self.backup_current_config()
            if backup_file:
                print(f"📦 已备份当前配置到: {backup_file}")
            
            # 复制住宅代理配置
            shutil.copy2(self.residential_config, self.config_file)
            print("✅ 已切换到住宅代理配置")
            
            # 显示配置摘要
            self.show_config_summary("residential")
            return True
            
        except Exception as e:
            print(f"❌ 切换配置失败: {e}")
            return False
    
    def switch_to_datacenter(self) -> bool:
        """切换到数据中心代理配置"""
        try:
            # 查找最近的数据中心配置备份
            datacenter_backup = self.find_datacenter_backup()
            
            if not datacenter_backup:
                print("❌ 未找到数据中心代理配置备份")
                print("💡 请手动恢复或重新配置数据中心代理")
                return False
            
            # 备份当前配置
            backup_file = self.backup_current_config()
            if backup_file:
                print(f"📦 已备份当前配置到: {backup_file}")
            
            # 恢复数据中心配置
            shutil.copy2(datacenter_backup, self.config_file)
            print(f"✅ 已切换到数据中心代理配置")
            print(f"📁 使用的配置文件: {datacenter_backup}")
            
            # 显示配置摘要
            self.show_config_summary("datacenter")
            return True
            
        except Exception as e:
            print(f"❌ 切换配置失败: {e}")
            return False
    
    def find_datacenter_backup(self) -> Path:
        """查找数据中心代理配置备份"""
        backup_files = list(self.backup_dir.glob("config_backup_*.py"))
        
        if not backup_files:
            return None
        
        # 返回最新的备份文件
        return max(backup_files, key=lambda x: x.stat().st_mtime)
    
    def show_config_summary(self, config_type: str):
        """显示配置摘要"""
        try:
            # 动态导入当前配置
            import importlib.util
            spec = importlib.util.spec_from_file_location("current_config", self.config_file)
            config_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(config_module)
            
            spider_config = getattr(config_module, 'SPIDER_CONFIG', {})
            proxy_config = getattr(config_module, 'PROXY_CONFIG', {})
            
            print("\n" + "="*50)
            print(f"📊 当前配置摘要 ({config_type.upper()})")
            print("="*50)
            print(f"请求间隔: {spider_config.get('request_delay', 'N/A')} 秒")
            print(f"并发请求: {spider_config.get('concurrent_requests', 'N/A')}")
            print(f"请求超时: {spider_config.get('timeout', 'N/A')} 秒")
            print(f"最大重试: {spider_config.get('max_retries', 'N/A')}")
            print(f"每轮爬取: {spider_config.get('max_channels_per_run', 'N/A')} 个频道")
            print(f"代理启用: {'是' if proxy_config.get('enabled') else '否'}")
            print(f"代理超时: {proxy_config.get('proxy_timeout', 'N/A')} 秒")
            print("="*50)
            
        except Exception as e:
            print(f"⚠️  无法显示配置摘要: {e}")
    
    def show_status(self):
        """显示当前配置状态"""
        try:
            # 检查当前配置类型
            with open(self.config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            config_type = "未知"
            if "住宅代理优化配置" in content or "RESIDENTIAL_PROXY_CONFIG" in content:
                config_type = "住宅代理配置"
            elif "数据中心代理" in content or "request_delay': (2, 5)" in content:
                config_type = "数据中心代理配置"
            
            print(f"📋 当前配置类型: {config_type}")
            
            # 显示配置摘要
            if "住宅" in config_type:
                self.show_config_summary("residential")
            else:
                self.show_config_summary("datacenter")
            
            # 显示备份文件
            backup_files = list(self.backup_dir.glob("config_backup_*.py"))
            if backup_files:
                print(f"\n📁 可用的配置备份 ({len(backup_files)} 个):")
                for backup in sorted(backup_files, key=lambda x: x.stat().st_mtime, reverse=True)[:5]:
                    mtime = backup.stat().st_mtime
                    import datetime
                    time_str = datetime.datetime.fromtimestamp(mtime).strftime("%Y-%m-%d %H:%M:%S")
                    print(f"  - {backup.name} ({time_str})")
            
        except Exception as e:
            print(f"❌ 获取状态失败: {e}")
    
    def create_custom_config(self):
        """创建自定义配置"""
        print("🛠️  自定义配置向导")
        print("="*40)
        
        try:
            # 选择基础配置
            print("选择基础配置:")
            print("1. 住宅代理配置（推荐）")
            print("2. 数据中心代理配置")
            
            choice = input("请选择 (1/2): ").strip()
            
            if choice == '1':
                base_config = self.residential_config
                config_name = "住宅代理"
            elif choice == '2':
                # 使用当前配置作为数据中心配置基础
                base_config = self.config_file
                config_name = "数据中心代理"
            else:
                print("❌ 无效选择")
                return False
            
            # 自定义参数
            print(f"\n基于 {config_name} 配置进行自定义:")
            
            request_delay_min = input("请求间隔最小值（秒，默认4）: ").strip() or "4"
            request_delay_max = input("请求间隔最大值（秒，默认8）: ").strip() or "8"
            concurrent_requests = input("并发请求数（默认2）: ").strip() or "2"
            max_channels = input("每轮最大爬取频道数（默认80）: ").strip() or "80"
            
            # 生成自定义配置
            custom_config_content = self.generate_custom_config(
                base_config,
                {
                    'request_delay_min': int(request_delay_min),
                    'request_delay_max': int(request_delay_max),
                    'concurrent_requests': int(concurrent_requests),
                    'max_channels': int(max_channels),
                }
            )
            
            # 保存自定义配置
            custom_config_file = self.project_root / 'config_custom.py'
            with open(custom_config_file, 'w', encoding='utf-8') as f:
                f.write(custom_config_content)
            
            print(f"✅ 自定义配置已保存到: {custom_config_file}")
            
            # 询问是否应用
            apply = input("是否立即应用此配置? (y/n): ").strip().lower()
            if apply == 'y':
                shutil.copy2(custom_config_file, self.config_file)
                print("✅ 自定义配置已应用")
                self.show_config_summary("custom")
            
            return True
            
        except Exception as e:
            print(f"❌ 创建自定义配置失败: {e}")
            return False
    
    def generate_custom_config(self, base_config: Path, params: Dict[str, Any]) -> str:
        """生成自定义配置内容"""
        with open(base_config, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换配置参数
        import re
        
        # 替换请求间隔
        content = re.sub(
            r"'request_delay': \(\d+, \d+\)",
            f"'request_delay': ({params['request_delay_min']}, {params['request_delay_max']})",
            content
        )
        
        # 替换并发请求数
        content = re.sub(
            r"'concurrent_requests': \d+",
            f"'concurrent_requests': {params['concurrent_requests']}",
            content
        )
        
        # 替换最大频道数
        content = re.sub(
            r"'max_channels_per_run': \d+",
            f"'max_channels_per_run': {params['max_channels']}",
            content
        )
        
        # 添加自定义标记
        content = f"""# 自定义配置文件
# 基于: {base_config.name}
# 生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

{content}
"""
        
        return content


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("YouTube Twitter爬虫配置切换工具")
        print("")
        print("用法:")
        print("  python switch_config.py residential  # 切换到住宅代理配置（推荐）")
        print("  python switch_config.py datacenter   # 切换到数据中心代理配置")
        print("  python switch_config.py status       # 查看当前配置状态")
        print("  python switch_config.py custom       # 创建自定义配置")
        print("")
        print("推荐使用住宅代理配置以获得更好的成功率！")
        return
    
    switcher = ConfigSwitcher()
    command = sys.argv[1].lower()
    
    if command == 'residential':
        success = switcher.switch_to_residential()
        if success:
            print("\n💡 提示:")
            print("  - 住宅代理成功率更高，但速度可能较慢")
            print("  - 请确保已配置 Webshare 住宅代理")
            print("  - 运行: python webshare_setup.py -i 进行代理配置")
    
    elif command == 'datacenter':
        success = switcher.switch_to_datacenter()
        if success:
            print("\n⚠️  注意:")
            print("  - 数据中心代理可能更容易被YouTube检测")
            print("  - 建议增加请求间隔，降低并发数")
            print("  - 如遇到频繁封禁，请切换回住宅代理")
    
    elif command == 'status':
        switcher.show_status()
    
    elif command == 'custom':
        switcher.create_custom_config()
    
    else:
        print(f"❌ 未知命令: {command}")
        print("使用 'python switch_config.py' 查看帮助")


if __name__ == '__main__':
    main()
