#!/usr/bin/env python3
"""
Webshare.io 代理配置工具
自动配置和测试 Webshare 代理服务

功能:
1. 批量导入 Webshare 代理列表
2. 自动格式转换
3. 代理连接测试
4. 配置文件生成

作者: GreenJoson
创建时间: 2025-06-26
版本: v1.0
"""

import sys
import json
import requests
from pathlib import Path
from typing import List, Dict, Optional
from loguru import logger

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from proxy_manager import ProxyManager
from config import PROXY_CONFIG


class WebshareConfigurator:
    """Webshare代理配置器"""
    
    def __init__(self):
        """初始化配置器"""
        self.proxy_manager = ProxyManager()
        self.project_root = Path(__file__).parent
        logger.info("Webshare配置器初始化完成")
    
    def convert_webshare_list(self, input_file: str, username: str, password: str, 
                             output_file: str = "proxy_list.txt") -> bool:
        """
        转换Webshare代理列表格式
        
        Args:
            input_file: 输入文件路径（Webshare导出格式）
            username: Webshare用户名
            password: Webshare密码
            output_file: 输出文件路径
            
        Returns:
            bool: 转换是否成功
        """
        try:
            input_path = Path(input_file)
            output_path = self.project_root / output_file
            
            if not input_path.exists():
                logger.error(f"输入文件不存在: {input_file}")
                return False
            
            # 读取输入文件
            with open(input_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 转换格式并写入输出文件
            converted_count = 0
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("# Webshare.io 代理配置\n")
                f.write(f"# 自动生成于: {logger._core.handlers[0]._sink._stream.name}\n")
                f.write(f"# 用户名: {username}\n")
                f.write("# 格式: username:password@ip:port\n\n")
                
                for line in lines:
                    line = line.strip()
                    
                    # 跳过空行和注释
                    if not line or line.startswith('#'):
                        continue
                    
                    # 处理不同的输入格式
                    if self._is_valid_proxy_format(line):
                        proxy_line = self._format_webshare_proxy(line, username, password)
                        if proxy_line:
                            f.write(proxy_line + '\n')
                            converted_count += 1
            
            logger.success(f"成功转换 {converted_count} 个代理到 {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"转换代理列表失败: {e}")
            return False
    
    def _is_valid_proxy_format(self, line: str) -> bool:
        """
        检查代理格式是否有效
        
        Args:
            line: 代理配置行
            
        Returns:
            bool: 是否有效
        """
        # 支持的格式:
        # ip:port
        # ip:port:username:password
        # username:password@ip:port
        
        if ':' not in line:
            return False
        
        parts = line.split(':')
        return len(parts) >= 2
    
    def _format_webshare_proxy(self, line: str, username: str, password: str) -> Optional[str]:
        """
        格式化Webshare代理配置
        
        Args:
            line: 原始代理行
            username: 用户名
            password: 密码
            
        Returns:
            str: 格式化后的代理配置
        """
        try:
            line = line.strip()
            
            # 如果已经包含认证信息，直接返回
            if '@' in line:
                return line
            
            # 处理 ip:port 格式
            if line.count(':') == 1:
                ip, port = line.split(':')
                return f"{username}:{password}@{ip}:{port}"
            
            # 处理 ip:port:username:password 格式
            elif line.count(':') == 3:
                ip, port, user, pwd = line.split(':')
                return f"{user}:{pwd}@{ip}:{port}"
            
            # 其他格式，尝试直接使用
            else:
                return f"{username}:{password}@{line}"
                
        except Exception as e:
            logger.warning(f"格式化代理失败: {line}, 错误: {e}")
            return None
    
    def test_webshare_proxies(self, proxy_file: str = "proxy_list.txt", 
                             max_test: int = 5) -> Dict:
        """
        测试Webshare代理连接
        
        Args:
            proxy_file: 代理文件路径
            max_test: 最大测试数量
            
        Returns:
            Dict: 测试结果统计
        """
        try:
            proxy_path = self.project_root / proxy_file
            
            if not proxy_path.exists():
                logger.error(f"代理文件不存在: {proxy_file}")
                return {}
            
            # 重新加载代理列表
            self.proxy_manager.load_proxies()
            
            if not self.proxy_manager.proxies:
                logger.error("没有可用的代理")
                return {}
            
            # 测试代理
            test_results = {
                'total_tested': 0,
                'working_proxies': 0,
                'failed_proxies': 0,
                'average_response_time': 0,
                'working_proxy_list': [],
                'failed_proxy_list': []
            }
            
            total_response_time = 0
            test_count = min(len(self.proxy_manager.proxies), max_test)
            
            logger.info(f"开始测试 {test_count} 个代理...")
            
            for i in range(test_count):
                proxy = self.proxy_manager.proxies[i]
                proxy_str = str(proxy)
                
                logger.info(f"测试代理 {i+1}/{test_count}: {proxy_str[:50]}...")
                
                is_working, response_time = self.proxy_manager.test_proxy(proxy)
                test_results['total_tested'] += 1
                
                if is_working:
                    test_results['working_proxies'] += 1
                    test_results['working_proxy_list'].append(proxy_str)
                    total_response_time += response_time
                    logger.success(f"✓ 代理可用，响应时间: {response_time:.2f}秒")
                else:
                    test_results['failed_proxies'] += 1
                    test_results['failed_proxy_list'].append(proxy_str)
                    logger.warning(f"✗ 代理不可用")
            
            # 计算平均响应时间
            if test_results['working_proxies'] > 0:
                test_results['average_response_time'] = total_response_time / test_results['working_proxies']
            
            # 输出测试结果
            logger.info("=" * 60)
            logger.info("代理测试结果:")
            logger.info(f"  总测试数: {test_results['total_tested']}")
            logger.info(f"  可用代理: {test_results['working_proxies']}")
            logger.info(f"  失败代理: {test_results['failed_proxies']}")
            logger.info(f"  成功率: {test_results['working_proxies']/test_results['total_tested']*100:.1f}%")
            logger.info(f"  平均响应时间: {test_results['average_response_time']:.2f}秒")
            logger.info("=" * 60)
            
            return test_results
            
        except Exception as e:
            logger.error(f"测试代理失败: {e}")
            return {}
    
    def create_webshare_config(self, username: str, password: str, 
                              proxy_endpoints: List[str]) -> bool:
        """
        创建Webshare代理配置文件
        
        Args:
            username: Webshare用户名
            password: Webshare密码
            proxy_endpoints: 代理端点列表
            
        Returns:
            bool: 创建是否成功
        """
        try:
            config_path = self.project_root / "proxy_list.txt"
            
            with open(config_path, 'w', encoding='utf-8') as f:
                f.write("# Webshare.io 代理配置\n")
                f.write(f"# 用户名: {username}\n")
                f.write(f"# 代理数量: {len(proxy_endpoints)}\n")
                f.write("# 格式: username:password@ip:port\n\n")
                
                for endpoint in proxy_endpoints:
                    if ':' in endpoint:
                        proxy_line = f"{username}:{password}@{endpoint}"
                        f.write(proxy_line + '\n')
            
            logger.success(f"成功创建代理配置文件: {config_path}")
            logger.info(f"配置了 {len(proxy_endpoints)} 个代理")
            return True
            
        except Exception as e:
            logger.error(f"创建配置文件失败: {e}")
            return False
    
    def interactive_setup(self):
        """交互式设置Webshare代理"""
        print("\n" + "="*60)
        print("Webshare.io 代理配置向导")
        print("="*60)
        
        try:
            # 获取用户输入
            username = input("请输入Webshare用户名: ").strip()
            if not username:
                print("用户名不能为空")
                return False
            
            password = input("请输入Webshare密码: ").strip()
            if not password:
                print("密码不能为空")
                return False
            
            print("\n选择配置方式:")
            print("1. 手动输入代理端点")
            print("2. 从文件导入代理列表")
            
            choice = input("请选择 (1/2): ").strip()
            
            if choice == '1':
                # 手动输入
                endpoints = []
                print("\n请输入代理端点 (格式: ip:port)，输入空行结束:")
                
                while True:
                    endpoint = input("代理端点: ").strip()
                    if not endpoint:
                        break
                    if ':' in endpoint:
                        endpoints.append(endpoint)
                    else:
                        print("格式错误，请使用 ip:port 格式")
                
                if endpoints:
                    success = self.create_webshare_config(username, password, endpoints)
                    if success:
                        print(f"\n✓ 成功配置 {len(endpoints)} 个代理")
                        
                        # 测试代理
                        test_choice = input("是否测试代理连接? (y/n): ").strip().lower()
                        if test_choice == 'y':
                            self.test_webshare_proxies(max_test=min(3, len(endpoints)))
                else:
                    print("没有输入任何代理端点")
                    return False
            
            elif choice == '2':
                # 从文件导入
                file_path = input("请输入代理文件路径: ").strip()
                if not file_path:
                    print("文件路径不能为空")
                    return False
                
                success = self.convert_webshare_list(file_path, username, password)
                if success:
                    print("\n✓ 代理列表转换成功")
                    
                    # 测试代理
                    test_choice = input("是否测试代理连接? (y/n): ").strip().lower()
                    if test_choice == 'y':
                        self.test_webshare_proxies(max_test=5)
                else:
                    print("代理列表转换失败")
                    return False
            
            else:
                print("无效选择")
                return False
            
            print("\n" + "="*60)
            print("Webshare代理配置完成！")
            print("现在可以运行爬虫程序了:")
            print("  python main.py --mode once    # 单次运行")
            print("  python main.py --mode continuous  # 持续运行")
            print("="*60)
            
            return True
            
        except KeyboardInterrupt:
            print("\n\n配置已取消")
            return False
        except Exception as e:
            logger.error(f"交互式设置失败: {e}")
            return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Webshare.io 代理配置工具')
    parser.add_argument('--interactive', '-i', action='store_true',
                       help='交互式配置模式')
    parser.add_argument('--test', '-t', action='store_true',
                       help='测试现有代理')
    parser.add_argument('--convert', '-c', type=str,
                       help='转换代理文件格式')
    parser.add_argument('--username', '-u', type=str,
                       help='Webshare用户名')
    parser.add_argument('--password', '-p', type=str,
                       help='Webshare密码')
    
    args = parser.parse_args()
    
    configurator = WebshareConfigurator()
    
    if args.interactive:
        configurator.interactive_setup()
    elif args.test:
        configurator.test_webshare_proxies()
    elif args.convert:
        if not args.username or not args.password:
            print("转换模式需要提供用户名和密码")
            print("使用: python webshare_setup.py -c 文件路径 -u 用户名 -p 密码")
            sys.exit(1)
        configurator.convert_webshare_list(args.convert, args.username, args.password)
    else:
        print("Webshare.io 代理配置工具")
        print("使用 -h 查看帮助信息")
        print("推荐使用交互式模式: python webshare_setup.py -i")


if __name__ == '__main__':
    main()
