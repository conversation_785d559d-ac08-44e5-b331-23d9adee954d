#!/usr/bin/env python3
"""
Webshare代理连接测试脚本
测试您购买的Webshare Rotating Residential代理是否正常工作

作者: GreenJoson
创建时间: 2025-06-26
"""

import requests
import time
import json
from typing import Dict, List


def test_single_proxy(username: str, password: str, proxy_host: str, proxy_port: int) -> Dict:
    """
    测试单个代理连接
    
    Args:
        username: 代理用户名
        password: 代理密码
        proxy_host: 代理主机
        proxy_port: 代理端口
        
    Returns:
        Dict: 测试结果
    """
    proxy_url = f"http://{username}:{password}@{proxy_host}:{proxy_port}"
    proxies = {
        "http": proxy_url,
        "https": proxy_url
    }
    
    test_result = {
        'proxy': f"{username}@{proxy_host}:{proxy_port}",
        'success': False,
        'ip': None,
        'country': None,
        'response_time': 0,
        'error': None
    }
    
    try:
        start_time = time.time()
        
        # 测试IP获取
        response = requests.get(
            "https://httpbin.org/ip",
            proxies=proxies,
            timeout=15,
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }
        )
        
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            ip_data = response.json()
            test_result['success'] = True
            test_result['ip'] = ip_data.get('origin')
            test_result['response_time'] = response_time
            
            # 获取地理位置信息
            try:
                geo_response = requests.get(
                    f"http://ip-api.com/json/{test_result['ip']}",
                    timeout=10
                )
                if geo_response.status_code == 200:
                    geo_data = geo_response.json()
                    test_result['country'] = geo_data.get('country')
                    test_result['city'] = geo_data.get('city')
                    test_result['region'] = geo_data.get('regionName')
            except:
                pass  # 地理位置获取失败不影响主要测试
                
        else:
            test_result['error'] = f"HTTP {response.status_code}"
            
    except requests.exceptions.Timeout:
        test_result['error'] = "连接超时"
    except requests.exceptions.ProxyError:
        test_result['error'] = "代理连接失败"
    except requests.exceptions.ConnectionError:
        test_result['error'] = "网络连接错误"
    except Exception as e:
        test_result['error'] = str(e)
    
    return test_result


def test_webshare_proxies():
    """测试所有Webshare代理"""
    print("🔍 开始测试 Webshare Rotating Residential 代理...")
    print("=" * 70)
    
    # Webshare代理配置
    proxy_host = "p.webshare.io"
    proxy_port = 80
    password = "6lwffvf16jjn222"
    
    # 用户名列表
    usernames = [f"gorkpiln-{i}" for i in range(1, 11)]
    
    results = []
    working_proxies = 0
    
    for i, username in enumerate(usernames, 1):
        print(f"[{i}/10] 测试代理: {username}@{proxy_host}:{proxy_port}")
        
        result = test_single_proxy(username, password, proxy_host, proxy_port)
        results.append(result)
        
        if result['success']:
            working_proxies += 1
            print(f"  ✅ 成功 - IP: {result['ip']}")
            if result.get('country'):
                print(f"     位置: {result.get('city', '')}, {result.get('region', '')}, {result['country']}")
            print(f"     响应时间: {result['response_time']:.2f}秒")
        else:
            print(f"  ❌ 失败 - 错误: {result['error']}")
        
        print()
        
        # 避免请求过快
        if i < len(usernames):
            time.sleep(2)
    
    # 输出测试总结
    print("=" * 70)
    print("📊 测试总结:")
    print(f"  总代理数: {len(usernames)}")
    print(f"  可用代理: {working_proxies}")
    print(f"  成功率: {working_proxies/len(usernames)*100:.1f}%")
    
    if working_proxies > 0:
        avg_response_time = sum(r['response_time'] for r in results if r['success']) / working_proxies
        print(f"  平均响应时间: {avg_response_time:.2f}秒")
        
        # 显示IP分布
        countries = {}
        for result in results:
            if result['success'] and result.get('country'):
                country = result['country']
                countries[country] = countries.get(country, 0) + 1
        
        if countries:
            print(f"  IP地理分布:")
            for country, count in countries.items():
                print(f"    {country}: {count} 个")
    
    print("=" * 70)
    
    if working_proxies >= 8:
        print("🎉 代理测试通过！可以开始使用爬虫了。")
        print("\n📝 下一步操作:")
        print("  1. 切换到住宅代理配置: python switch_config.py residential")
        print("  2. 运行测试: python test_v1.py")
        print("  3. 开始爬取: python main.py --mode once")
    elif working_proxies >= 5:
        print("⚠️  部分代理可用，建议联系Webshare客服检查。")
    else:
        print("❌ 大部分代理不可用，请检查:")
        print("  1. 用户名和密码是否正确")
        print("  2. 代理套餐是否已激活")
        print("  3. 网络连接是否正常")
    
    return results


def test_youtube_access():
    """测试通过代理访问YouTube"""
    print("\n🎯 测试通过代理访问YouTube...")
    
    # 使用第一个代理测试
    proxy_url = "http://gorkpiln-1:<EMAIL>:80"
    proxies = {
        "http": proxy_url,
        "https": proxy_url
    }
    
    try:
        response = requests.get(
            "https://www.youtube.com",
            proxies=proxies,
            timeout=20,
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }
        )
        
        if response.status_code == 200:
            print("✅ YouTube访问成功！")
            if "YouTube" in response.text:
                print("✅ 页面内容正常")
            else:
                print("⚠️  页面内容异常，可能被重定向")
        else:
            print(f"❌ YouTube访问失败，状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ YouTube访问异常: {e}")


if __name__ == '__main__':
    print("Webshare.io Rotating Residential 代理测试")
    print("套餐: $7/月 - 80 million IPs/month")
    print()
    
    # 测试代理连接
    results = test_webshare_proxies()
    
    # 测试YouTube访问
    working_count = sum(1 for r in results if r['success'])
    if working_count > 0:
        test_youtube_access()
    
    print("\n🔗 相关链接:")
    print("  Webshare控制台: https://webshare.io/dashboard")
    print("  使用文档: https://docs.webshare.io/")
    print("  客服支持: https://webshare.io/support")
