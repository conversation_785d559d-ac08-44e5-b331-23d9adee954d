#!/usr/bin/env python3
"""
测试SOCKS5代理连接
尝试使用SOCKS5协议连接Webshare代理

作者: GreenJoson
创建时间: 2025-06-26
"""

import requests
import time

def test_socks5_proxy():
    """测试SOCKS5代理连接"""
    print("🔍 测试SOCKS5代理连接")
    print("=" * 50)
    
    # Webshare代理信息
    username = "gorkpiln"
    password = "6lwffvf16jjn"
    
    # 测试不同的SOCKS5端点和端口
    socks5_configs = [
        # 标准SOCKS5端口
        {"host": "p.webshare.io", "port": 1080},
        {"host": "p.webshare.io", "port": 1085},
        {"host": "rotating-residential.webshare.io", "port": 1080},
        {"host": "proxy.webshare.io", "port": 1080},
        # 有些服务商使用HTTP端口也支持SOCKS5
        {"host": "p.webshare.io", "port": 80},
        {"host": "p.webshare.io", "port": 8080},
    ]
    
    for i, config in enumerate(socks5_configs, 1):
        print(f"\n[{i}/{len(socks5_configs)}] 测试SOCKS5: {config['host']}:{config['port']}")
        
        # SOCKS5代理URL格式
        socks5_url = f"socks5://{username}:{password}@{config['host']}:{config['port']}"
        
        proxies = {
            "http": socks5_url,
            "https": socks5_url
        }
        
        print(f"  代理URL: {socks5_url}")
        
        try:
            start_time = time.time()
            response = requests.get(
                "http://httpbin.org/ip",
                proxies=proxies,
                timeout=20,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            )
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                print(f"  ✅ SOCKS5连接成功!")
                print(f"    代理IP: {data.get('origin')}")
                print(f"    响应时间: {response_time:.2f}秒")
                
                # 测试HTTPS
                try:
                    https_response = requests.get(
                        "https://httpbin.org/ip",
                        proxies=proxies,
                        timeout=20,
                        headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                    )
                    
                    if https_response.status_code == 200:
                        print(f"  ✅ HTTPS也正常工作!")
                        
                        # 测试YouTube
                        test_youtube_socks5(proxies)
                        
                        return {
                            "success": True,
                            "config": config,
                            "proxy_url": socks5_url,
                            "proxy_ip": data.get('origin')
                        }
                    else:
                        print(f"  ❌ HTTPS失败 - 状态码: {https_response.status_code}")
                        
                except Exception as e:
                    print(f"  ❌ HTTPS测试异常: {str(e)[:100]}...")
                
            else:
                print(f"  ❌ 连接失败 - 状态码: {response.status_code}")
                
        except requests.exceptions.ProxyError as e:
            print(f"  ❌ SOCKS5代理错误: {str(e)[:100]}...")
        except requests.exceptions.Timeout:
            print(f"  ❌ 连接超时")
        except requests.exceptions.ConnectionError as e:
            print(f"  ❌ 连接错误: {str(e)[:100]}...")
        except Exception as e:
            print(f"  ❌ 其他错误: {str(e)[:100]}...")
        
        time.sleep(2)  # 避免请求过快
    
    return {"success": False}

def test_youtube_socks5(proxies):
    """通过SOCKS5测试YouTube访问"""
    print("  🎯 测试YouTube访问...")
    
    try:
        response = requests.get(
            "https://www.youtube.com",
            proxies=proxies,
            timeout=25,
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }
        )
        
        if response.status_code == 200:
            if "YouTube" in response.text:
                print("    ✅ YouTube访问成功!")
            else:
                print("    ⚠️  YouTube访问成功但内容异常")
        else:
            print(f"    ❌ YouTube访问失败 - 状态码: {response.status_code}")
            
    except Exception as e:
        print(f"    ❌ YouTube访问异常: {str(e)[:80]}...")

def test_mixed_protocols():
    """测试混合协议 - HTTP代理 + SOCKS5"""
    print("\n🔄 测试混合协议配置...")
    
    username = "gorkpiln"
    password = "6lwffvf16jjn"
    
    # 混合配置：HTTP用于HTTP请求，SOCKS5用于HTTPS请求
    mixed_configs = [
        {
            "name": "HTTP+SOCKS5混合",
            "proxies": {
                "http": f"http://{username}:{password}@p.webshare.io:80",
                "https": f"socks5://{username}:{password}@p.webshare.io:1080"
            }
        },
        {
            "name": "全SOCKS5",
            "proxies": {
                "http": f"socks5://{username}:{password}@p.webshare.io:1080",
                "https": f"socks5://{username}:{password}@p.webshare.io:1080"
            }
        }
    ]
    
    for config in mixed_configs:
        print(f"\n测试配置: {config['name']}")
        
        try:
            response = requests.get(
                "https://httpbin.org/ip",
                proxies=config["proxies"],
                timeout=15,
                headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"  ✅ {config['name']} 成功!")
                print(f"    代理IP: {data.get('origin')}")
                return config
            else:
                print(f"  ❌ {config['name']} 失败 - 状态码: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ {config['name']} 异常: {str(e)[:100]}...")
    
    return None

def check_socks_support():
    """检查系统是否支持SOCKS代理"""
    print("🔧 检查SOCKS支持...")
    
    try:
        import socks
        print("  ✅ PySocks库已安装")
        return True
    except ImportError:
        print("  ❌ PySocks库未安装")
        print("  💡 安装命令: pip install PySocks")
        return False

def install_pysocks():
    """安装PySocks库"""
    print("\n📦 安装PySocks库...")
    
    import subprocess
    import sys
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "PySocks"
        ])
        print("  ✅ PySocks安装成功!")
        return True
    except Exception as e:
        print(f"  ❌ PySocks安装失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 SOCKS5代理测试工具")
    print("尝试使用SOCKS5协议绕过VPN干扰")
    print()
    
    # 检查SOCKS支持
    if not check_socks_support():
        if install_pysocks():
            print("  ✅ 重新导入...")
        else:
            print("  ❌ 无法安装PySocks，将尝试基本SOCKS5测试")
    
    # 测试SOCKS5代理
    result = test_socks5_proxy()
    
    # 如果SOCKS5失败，尝试混合协议
    if not result["success"]:
        mixed_result = test_mixed_protocols()
        if mixed_result:
            result = {"success": True, "config": mixed_result}
    
    print("\n" + "=" * 50)
    print("📊 SOCKS5测试总结:")
    
    if result["success"]:
        print("🎉 SOCKS5代理测试成功!")
        
        if "proxy_ip" in result:
            print(f"✅ 代理IP: {result['proxy_ip']}")
            print(f"✅ 工作配置: {result.get('proxy_url', 'Mixed Protocol')}")
        
        print("\n📝 下一步:")
        print("1. 更新爬虫配置使用SOCKS5")
        print("2. 运行: python test_v1.py")
        print("3. 开始爬取: python main.py --mode once")
        
    else:
        print("❌ SOCKS5代理也失败了")
        print("\n🔍 这表明问题可能是:")
        print("1. VPN完全阻止了到Webshare的连接")
        print("2. 网络防火墙限制")
        print("3. Webshare服务器维护")
        print("\n💡 最终建议:")
        print("1. 完全关闭VPN重新测试")
        print("2. 尝试手机热点网络")
        print("3. 先用无代理模式开发: python test_no_proxy.py")
        print("4. 联系Webshare技术支持")

if __name__ == '__main__':
    main()
