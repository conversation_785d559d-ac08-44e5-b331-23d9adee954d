#!/usr/bin/env python3
"""
调试爬虫问题
找出 unhashable type: 'dict' 错误的原因

作者: GreenJoson
创建时间: 2025-06-26
"""

import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from config import YOUTUBE_CONFIG
from utils import rate_limiter

def debug_config():
    """调试配置"""
    print("🔍 调试配置信息")
    print("=" * 50)
    
    print("YOUTUBE_CONFIG:")
    for key, value in YOUTUBE_CONFIG.items():
        print(f"  {key}: {type(value)} = {value}")
    
    print(f"\nsearch_keywords类型: {type(YOUTUBE_CONFIG['search_keywords'])}")
    print(f"search_keywords内容: {YOUTUBE_CONFIG['search_keywords']}")

def debug_rate_limiter():
    """调试频率限制器"""
    print("\n🔍 调试频率限制器")
    print("=" * 50)
    
    print(f"rate_limiter类型: {type(rate_limiter)}")
    print(f"rate_limiter: {rate_limiter}")
    
    try:
        print("测试rate_limiter.wait()...")
        rate_limiter.wait()
        print("✅ rate_limiter.wait() 正常")
    except Exception as e:
        print(f"❌ rate_limiter.wait() 异常: {e}")

def debug_set_operations():
    """调试集合操作"""
    print("\n🔍 调试集合操作")
    print("=" * 50)
    
    # 模拟问题场景
    discovered_urls = set()
    print(f"discovered_urls类型: {type(discovered_urls)}")
    
    # 测试不同类型的update操作
    test_cases = [
        ["url1", "url2"],  # 列表
        ("url3", "url4"),  # 元组
        {"url5", "url6"},  # 集合
        {"key": "value"},  # 字典 - 这会导致错误
    ]
    
    for i, test_data in enumerate(test_cases):
        try:
            print(f"测试 {i+1}: {type(test_data)} = {test_data}")
            temp_set = set()
            temp_set.update(test_data)
            print(f"  ✅ 成功: {temp_set}")
        except Exception as e:
            print(f"  ❌ 失败: {e}")

def debug_search_function():
    """调试搜索函数"""
    print("\n🔍 调试搜索函数模拟")
    print("=" * 50)
    
    # 模拟_search_channels_by_keyword的返回值
    def mock_search_channels_by_keyword(keyword, max_results):
        """模拟搜索函数"""
        print(f"  模拟搜索: {keyword}, max_results: {max_results}")
        
        # 可能的返回值类型
        return_types = [
            ["url1", "url2"],  # 正确：列表
            {"url1", "url2"},  # 正确：集合
            {"keyword": keyword, "urls": ["url1", "url2"]},  # 错误：字典
            None,  # 错误：None
        ]
        
        # 随机返回一种类型来测试
        import random
        return random.choice(return_types)
    
    # 测试集合更新
    discovered_urls = set()
    search_keywords = YOUTUBE_CONFIG['search_keywords']
    
    for keyword in search_keywords[:3]:  # 只测试前3个
        try:
            print(f"\n测试关键词: {keyword}")
            urls = mock_search_channels_by_keyword(keyword, 10)
            print(f"  返回类型: {type(urls)}")
            print(f"  返回值: {urls}")
            
            if urls:
                discovered_urls.update(urls)
                print(f"  ✅ 更新成功，当前集合: {discovered_urls}")
            else:
                print(f"  ⚠️  返回值为空")
                
        except Exception as e:
            print(f"  ❌ 更新失败: {e}")
            print(f"     错误类型: {type(e)}")

def main():
    """主函数"""
    print("🐛 Spider调试工具")
    print("查找 'unhashable type: dict' 错误原因")
    print()
    
    debug_config()
    debug_rate_limiter()
    debug_set_operations()
    debug_search_function()
    
    print("\n" + "=" * 50)
    print("📊 调试总结:")
    print("1. 检查YOUTUBE_CONFIG配置是否正确")
    print("2. 检查rate_limiter是否正常工作")
    print("3. 检查集合操作是否有类型错误")
    print("4. 模拟搜索函数的不同返回值类型")
    print("\n💡 如果发现字典类型被传递给set.update()，")
    print("   说明_search_channels_by_keyword返回了字典而不是列表")

if __name__ == '__main__':
    main()
