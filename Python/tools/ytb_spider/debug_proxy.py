#!/usr/bin/env python3
"""
详细的代理调试脚本
逐步排查Webshare代理连接问题
"""

import requests
import socket
import time
from urllib.parse import urlparse

def check_dns_resolution():
    """检查DNS解析"""
    print("🔍 检查DNS解析...")
    
    hosts = [
        "p.webshare.io",
        "rotating-residential.webshare.io",
        "proxy.webshare.io"
    ]
    
    for host in hosts:
        try:
            ip = socket.gethostbyname(host)
            print(f"  ✅ {host} -> {ip}")
        except Exception as e:
            print(f"  ❌ {host} -> 解析失败: {e}")

def test_socket_connection():
    """测试Socket连接"""
    print("\n🔌 测试Socket连接...")
    
    endpoints = [
        ("p.webshare.io", 80),
        ("p.webshare.io", 8080),
        ("rotating-residential.webshare.io", 80)
    ]
    
    for host, port in endpoints:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"  ✅ {host}:{port} - 连接成功")
            else:
                print(f"  ❌ {host}:{port} - 连接失败 (错误码: {result})")
        except Exception as e:
            print(f"  ❌ {host}:{port} - 异常: {e}")

def test_proxy_auth():
    """测试代理认证"""
    print("\n🔐 测试代理认证...")
    
    # 使用控制台显示的确切信息
    username = "gorkpiln-1"
    password = "6lwffvf16jjn"  # 从控制台截图看到的密码
    
    # 不同的代理URL格式
    proxy_formats = [
        f"http://{username}:{password}@p.webshare.io:80",
        f"http://{username}:{password}@p.webshare.io:80/",
        f"http://{username}:{password}@rotating-residential.webshare.io:80",
    ]
    
    for i, proxy_url in enumerate(proxy_formats, 1):
        print(f"\n[{i}/3] 测试格式: {proxy_url}")
        
        proxies = {
            "http": proxy_url,
            "https": proxy_url
        }
        
        try:
            # 使用简单的HTTP请求
            response = requests.get(
                "http://httpbin.org/ip",
                proxies=proxies,
                timeout=15,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"  ✅ 成功! 代理IP: {data.get('origin')}")
                return proxy_url
            else:
                print(f"  ❌ HTTP错误: {response.status_code}")
                
        except requests.exceptions.ProxyError as e:
            print(f"  ❌ 代理错误: {str(e)[:100]}...")
        except requests.exceptions.Timeout:
            print(f"  ❌ 连接超时")
        except requests.exceptions.ConnectionError as e:
            print(f"  ❌ 连接错误: {str(e)[:100]}...")
        except Exception as e:
            print(f"  ❌ 其他错误: {str(e)[:100]}...")
    
    return None

def test_without_vpn_suggestion():
    """建议关闭VPN测试"""
    print("\n🌐 VPN影响测试...")
    print("当前检测到的IP: ************")
    print("这个IP已添加到Webshare白名单")
    print()
    print("💡 建议测试步骤:")
    print("1. 暂时关闭VPN")
    print("2. 重新运行此脚本")
    print("3. 如果成功，将新IP添加到白名单")
    print("4. 或者确保VPN IP稳定并已加入白名单")

def test_webshare_api():
    """测试Webshare API"""
    print("\n🔧 测试Webshare API...")
    
    try:
        # 测试获取代理列表
        response = requests.get(
            "https://proxy.webshare.io/api/v2/proxy/list/",
            auth=("gorkpiln-1", "6lwffvf16jjn"),
            timeout=10
        )
        
        print(f"API状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ API认证成功")
            try:
                data = response.json()
                if 'results' in data:
                    print(f"  可用代理数量: {len(data['results'])}")
                    if data['results']:
                        proxy = data['results'][0]
                        print(f"  第一个代理: {proxy.get('proxy_address')}:{proxy.get('port')}")
            except:
                print("  API响应解析失败")
        elif response.status_code == 401:
            print("❌ API认证失败 - 用户名或密码错误")
        else:
            print(f"❌ API错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")

def main():
    """主函数"""
    print("🔧 Webshare代理详细调试")
    print("=" * 50)
    
    # 1. DNS解析测试
    check_dns_resolution()
    
    # 2. Socket连接测试
    test_socket_connection()
    
    # 3. API测试
    test_webshare_api()
    
    # 4. 代理认证测试
    working_proxy = test_proxy_auth()
    
    # 5. VPN建议
    test_without_vpn_suggestion()
    
    print("\n" + "=" * 50)
    print("📋 调试总结:")
    
    if working_proxy:
        print(f"✅ 找到可用代理: {working_proxy}")
        print("\n📝 下一步:")
        print("1. 更新配置文件")
        print("2. 运行爬虫测试")
    else:
        print("❌ 代理连接失败")
        print("\n🔍 排查建议:")
        print("1. 关闭VPN重新测试")
        print("2. 检查防火墙设置")
        print("3. 尝试不同网络环境")
        print("4. 联系Webshare技术支持")

if __name__ == '__main__':
    main()
