#!/usr/bin/env python3
"""
调试代理问题
找出 unhashable type: 'dict' 错误的具体位置

作者: GreenJoson
创建时间: 2025-06-26
"""

import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from proxy_manager import ProxyManager
from config import PROXY_CONFIG

def debug_proxy_manager():
    """调试代理管理器"""
    print("🔍 调试代理管理器")
    print("=" * 50)
    
    # 初始化代理管理器
    proxy_manager = ProxyManager()
    
    print(f"代理配置: {PROXY_CONFIG}")
    print(f"加载的代理数量: {len(proxy_manager.proxies)}")
    
    if proxy_manager.proxies:
        print(f"代理列表:")
        for i, proxy in enumerate(proxy_manager.proxies, 1):
            print(f"  {i}. {type(proxy)} = {proxy}")
    
    # 测试获取随机代理
    print(f"\n测试获取随机代理:")
    try:
        proxy = proxy_manager.get_random_proxy()
        print(f"  返回类型: {type(proxy)}")
        print(f"  返回值: {proxy}")
        
        if proxy:
            print(f"  代理键: {list(proxy.keys())}")
            for key, value in proxy.items():
                print(f"    {key}: {type(value)} = {value}")
    except Exception as e:
        print(f"  ❌ 获取代理异常: {e}")
    
    # 测试请求头
    print(f"\n测试请求头:")
    try:
        headers = proxy_manager.get_request_headers()
        print(f"  返回类型: {type(headers)}")
        print(f"  返回值: {headers}")
    except Exception as e:
        print(f"  ❌ 获取请求头异常: {e}")

def debug_make_request():
    """调试make_request方法"""
    print(f"\n🔍 调试make_request方法")
    print("=" * 50)
    
    proxy_manager = ProxyManager()
    test_url = "https://httpbin.org/ip"
    
    print(f"测试URL: {test_url}")
    
    try:
        # 手动模拟make_request的步骤
        proxy = proxy_manager.get_random_proxy()
        headers = proxy_manager.get_request_headers()
        
        print(f"代理: {type(proxy)} = {proxy}")
        print(f"请求头: {type(headers)} = {headers}")
        
        # 这里应该会出现错误
        import requests
        
        print(f"\n尝试发送请求...")
        response = requests.get(
            test_url,
            proxies=proxy,  # 这里可能出错
            headers=headers,
            timeout=10
        )
        
        print(f"  ✅ 请求成功: {response.status_code}")
        
    except Exception as e:
        print(f"  ❌ 请求异常: {e}")
        print(f"  异常类型: {type(e)}")
        
        # 打印详细的错误信息
        import traceback
        print(f"  详细错误:")
        traceback.print_exc()

def debug_config():
    """调试配置"""
    print(f"\n🔍 调试配置")
    print("=" * 50)
    
    print(f"PROXY_CONFIG:")
    for key, value in PROXY_CONFIG.items():
        print(f"  {key}: {type(value)} = {value}")

def test_simple_request():
    """测试简单请求（无代理）"""
    print(f"\n🔍 测试简单请求（无代理）")
    print("=" * 50)
    
    import requests
    
    try:
        response = requests.get("https://httpbin.org/ip", timeout=10)
        print(f"  ✅ 无代理请求成功: {response.status_code}")
        print(f"  响应: {response.json()}")
    except Exception as e:
        print(f"  ❌ 无代理请求失败: {e}")

def main():
    """主函数"""
    print("🐛 代理问题调试工具")
    print("查找 'unhashable type: dict' 错误")
    print()
    
    debug_config()
    debug_proxy_manager()
    test_simple_request()
    debug_make_request()
    
    print(f"\n" + "=" * 50)
    print("📊 调试总结:")
    print("1. 检查代理配置是否正确")
    print("2. 检查代理解析是否正确")
    print("3. 检查requests.get参数是否正确")
    print("4. 找出具体的错误位置")

if __name__ == '__main__':
    main()
