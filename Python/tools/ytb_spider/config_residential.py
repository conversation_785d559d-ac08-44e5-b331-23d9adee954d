"""
针对动态住宅代理优化的配置文件
专门为Webshare.io动态住宅代理服务优化

使用方法:
1. 将此文件重命名为 config.py 替换原配置
2. 或者在原 config.py 中导入这些配置覆盖默认值

作者: GreenJoson
创建时间: 2025-06-26
版本: v1.0 - 住宅代理优化版
"""

import os
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent

# 数据库配置
DATABASE_CONFIG = {
    'db_path': PROJECT_ROOT / 'youtube_channels.db',
    'backup_interval': 3600,  # 备份间隔(秒)
}

# 爬虫配置 - 针对住宅代理优化
SPIDER_CONFIG = {
    'request_delay': (4, 8),  # 住宅代理需要更长间隔，模拟真实用户行为
    'max_retries': 3,  # 住宅代理可能有网络波动，增加重试次数
    'timeout': 25,  # 住宅代理响应可能较慢，增加超时时间
    'concurrent_requests': 2,  # 降低并发数，避免触发YouTube限制
    'max_channels_per_run': 80,  # 适中的爬取量，避免过于激进
    'discovery_interval': 7200,  # 增加发现间隔，减少搜索频率
    'crawl_batch_size': 20,  # 每批处理的频道数
    'rest_interval': 300,  # 每批之间的休息时间（秒）
}

# 代理配置 - 专为动态住宅代理优化
PROXY_CONFIG = {
    'enabled': True,  # 必须启用代理
    'proxy_list_file': PROJECT_ROOT / 'proxy_list.txt',
    'proxy_test_url': 'https://httpbin.org/ip',
    'proxy_timeout': 20,  # 住宅代理可能稍慢，增加超时时间
    'max_proxy_failures': 3,  # 住宅代理偶尔不稳定，允许更多失败
    'rotation_strategy': 'dynamic',  # 动态轮换策略
    'rotation_interval': 5,  # 更频繁轮换，充分利用动态特性
    'health_check_interval': 600,  # 代理健康检查间隔（秒）
    'sticky_session': False,  # 不使用粘性会话，充分利用动态轮换
}

# User-Agent列表 - 模拟真实用户
USER_AGENTS = [
    # Chrome - 最常见的浏览器
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    
    # Firefox
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0',
    
    # Safari
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15',
    
    # Edge
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
    
    # 移动端 User-Agent
    'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
    'Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
]

# YouTube相关配置 - 保守策略
YOUTUBE_CONFIG = {
    'base_url': 'https://www.youtube.com',
    'channel_url_patterns': [
        r'https://www\.youtube\.com/channel/([a-zA-Z0-9_-]+)',
        r'https://www\.youtube\.com/c/([a-zA-Z0-9_-]+)',
        r'https://www\.youtube\.com/@([a-zA-Z0-9_.-]+)',
        r'https://www\.youtube\.com/user/([a-zA-Z0-9_-]+)',
    ],
    # 使用更温和的搜索关键词，避免触发反爬
    'search_keywords': [
        'tutorial', 'review', 'vlog', 'music', 'cooking', 'travel',
        'tech', 'gaming', 'education', 'news', 'entertainment',
        'fitness', 'beauty', 'diy', 'art', 'science', 'business'
    ],
    'search_filters': {
        'upload_date': 'month',  # 只搜索最近一个月的内容
        'sort_by': 'relevance',  # 按相关性排序
        'duration': 'medium',    # 中等时长视频
    },
    'rate_limit': {
        'searches_per_hour': 20,  # 每小时最多20次搜索
        'channels_per_hour': 100, # 每小时最多爬取100个频道
    }
}

# Twitter/X链接识别配置
TWITTER_CONFIG = {
    'patterns': [
        r'https?://(?:www\.)?twitter\.com/([a-zA-Z0-9_]+)',
        r'https?://(?:www\.)?x\.com/([a-zA-Z0-9_]+)',
        r'@([a-zA-Z0-9_]+)',  # @username格式
        r'twitter\.com/([a-zA-Z0-9_]+)',  # 不带协议的格式
        r'x\.com/([a-zA-Z0-9_]+)',
    ],
    'domains': ['twitter.com', 'x.com'],
    'exclude_keywords': [
        'support', 'help', 'news', 'official', 'team', 'press',
        'business', 'ads', 'api', 'dev', 'developer'
    ],
}

# 日志配置 - 详细记录住宅代理使用情况
LOG_CONFIG = {
    'level': 'INFO',
    'format': '{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}',
    'rotation': '50 MB',  # 增加日志文件大小
    'retention': '14 days',  # 保留更长时间
    'log_file': PROJECT_ROOT / 'logs' / 'spider_residential.log',
    'proxy_log_file': PROJECT_ROOT / 'logs' / 'proxy_usage.log',  # 专门的代理使用日志
}

# 住宅代理特殊配置
RESIDENTIAL_PROXY_CONFIG = {
    'session_duration': 600,  # 会话持续时间（秒）
    'ip_rotation_frequency': 'high',  # IP轮换频率：low/medium/high
    'geo_targeting': 'US,CA,GB,AU',  # 地理位置定向
    'sticky_session': False,  # 不使用粘性会话
    'bandwidth_limit': '10GB',  # 带宽限制
    'concurrent_limit': 2,  # 并发连接限制
}

# 反检测配置
ANTI_DETECTION_CONFIG = {
    'randomize_headers': True,  # 随机化请求头
    'simulate_human_behavior': True,  # 模拟人类行为
    'mouse_movements': False,  # 不模拟鼠标移动（无头模式）
    'scroll_simulation': False,  # 不模拟滚动
    'typing_delays': True,  # 模拟打字延迟
    'random_pauses': True,  # 随机暂停
    'viewport_randomization': True,  # 随机化视口大小
}

# 错误处理配置
ERROR_HANDLING_CONFIG = {
    'max_consecutive_failures': 5,  # 最大连续失败次数
    'failure_backoff_factor': 2,  # 失败退避因子
    'circuit_breaker_threshold': 10,  # 熔断器阈值
    'recovery_time': 1800,  # 恢复时间（秒）
    'alert_on_high_failure_rate': True,  # 高失败率时告警
    'failure_rate_threshold': 0.3,  # 失败率阈值
}

# 性能监控配置
MONITORING_CONFIG = {
    'enable_metrics': True,  # 启用性能指标
    'metrics_interval': 300,  # 指标收集间隔（秒）
    'proxy_performance_tracking': True,  # 代理性能跟踪
    'success_rate_monitoring': True,  # 成功率监控
    'response_time_tracking': True,  # 响应时间跟踪
}

# 创建必要的目录
def create_directories():
    """创建项目所需的目录"""
    directories = [
        PROJECT_ROOT / 'logs',
        PROJECT_ROOT / 'data',
        PROJECT_ROOT / 'backups',
        PROJECT_ROOT / 'metrics',
    ]
    
    for directory in directories:
        directory.mkdir(exist_ok=True)

# 环境变量配置
def load_env_config():
    """加载环境变量配置"""
    from dotenv import load_dotenv
    load_dotenv()
    
    # Webshare住宅代理配置
    WEBSHARE_USERNAME = os.getenv('WEBSHARE_USERNAME')
    WEBSHARE_PASSWORD = os.getenv('WEBSHARE_PASSWORD')
    WEBSHARE_ENDPOINT = os.getenv('WEBSHARE_ENDPOINT')
    
    # YouTube API密钥(可选)
    YOUTUBE_API_KEY = os.getenv('YOUTUBE_API_KEY')
    
    return {
        'webshare_auth': {
            'username': WEBSHARE_USERNAME,
            'password': WEBSHARE_PASSWORD,
            'endpoint': WEBSHARE_ENDPOINT,
        } if WEBSHARE_USERNAME and WEBSHARE_PASSWORD else None,
        'youtube_api_key': YOUTUBE_API_KEY,
    }

# 验证住宅代理配置
def validate_residential_config():
    """验证住宅代理配置是否正确"""
    issues = []
    
    # 检查代理文件
    if not PROXY_CONFIG['proxy_list_file'].exists():
        issues.append("代理配置文件不存在，请创建 proxy_list.txt")
    
    # 检查请求间隔
    if SPIDER_CONFIG['request_delay'][0] < 3:
        issues.append("住宅代理建议请求间隔至少3秒")
    
    # 检查并发数
    if SPIDER_CONFIG['concurrent_requests'] > 3:
        issues.append("住宅代理建议并发数不超过3")
    
    # 检查环境变量
    env_config = load_env_config()
    if not env_config.get('webshare_auth'):
        issues.append("未配置Webshare认证信息，请设置环境变量")
    
    return issues

if __name__ == '__main__':
    create_directories()
    issues = validate_residential_config()
    
    if issues:
        print("⚠️  配置检查发现问题:")
        for issue in issues:
            print(f"  - {issue}")
    else:
        print("✅ 住宅代理配置检查通过")
    
    print(f"\n📊 当前配置摘要:")
    print(f"  请求间隔: {SPIDER_CONFIG['request_delay']} 秒")
    print(f"  并发请求: {SPIDER_CONFIG['concurrent_requests']}")
    print(f"  代理超时: {PROXY_CONFIG['proxy_timeout']} 秒")
    print(f"  轮换间隔: {PROXY_CONFIG['rotation_interval']} 个请求")
    print(f"  每轮爬取: {SPIDER_CONFIG['max_channels_per_run']} 个频道")
