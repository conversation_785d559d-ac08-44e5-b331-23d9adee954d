# YouTube Twitter爬虫环境配置文件示例
# 复制此文件为 .env 并填入真实配置

# YouTube API配置（可选）
# 如果有YouTube API密钥，可以提高爬取效率
YOUTUBE_API_KEY=your_youtube_api_key_here

# 代理配置（可选）
# 如果使用需要认证的代理服务
PROXY_USERNAME=your_proxy_username
PROXY_PASSWORD=your_proxy_password

# 数据库配置（可选）
# 默认使用SQLite，如需使用其他数据库可配置
DATABASE_URL=sqlite:///youtube_channels.db

# 日志级别配置
LOG_LEVEL=INFO

# 爬虫配置
MAX_CHANNELS_PER_RUN=100
REQUEST_DELAY_MIN=2
REQUEST_DELAY_MAX=5

# 代理配置
ENABLE_PROXY=true
PROXY_TIMEOUT=10
MAX_PROXY_FAILURES=3
