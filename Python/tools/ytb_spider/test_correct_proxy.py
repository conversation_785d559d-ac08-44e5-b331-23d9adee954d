#!/usr/bin/env python3
"""
使用正确用户名测试Webshare代理
根据API返回的正确用户名进行测试

作者: GreenJoson
创建时间: 2025-06-26
"""

import requests
import time

def test_webshare_proxy():
    """测试正确的Webshare代理配置"""
    print("🔍 测试Webshare代理 - 使用正确的用户名")
    print("=" * 50)
    
    # 使用API返回的正确信息
    username = "gorkpiln"  # 不是 gorkpiln-1
    password = "6lwffvf16jjn"
    
    # 测试不同的端点
    endpoints = [
        "p.webshare.io:80",
        "rotating-residential.webshare.io:80",
        "proxy.webshare.io:80"
    ]
    
    for i, endpoint in enumerate(endpoints, 1):
        print(f"\n[{i}/3] 测试端点: {endpoint}")
        
        proxy_url = f"http://{username}:{password}@{endpoint}"
        proxies = {
            "http": proxy_url,
            "https": proxy_url
        }
        
        print(f"  代理URL: {proxy_url}")
        
        try:
            # 测试HTTP请求
            start_time = time.time()
            response = requests.get(
                "http://httpbin.org/ip",
                proxies=proxies,
                timeout=20,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            )
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                print(f"  ✅ HTTP测试成功!")
                print(f"    代理IP: {data.get('origin')}")
                print(f"    响应时间: {response_time:.2f}秒")
                
                # 测试HTTPS请求
                try:
                    start_time = time.time()
                    https_response = requests.get(
                        "https://httpbin.org/ip",
                        proxies=proxies,
                        timeout=20,
                        headers={
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                        }
                    )
                    https_response_time = time.time() - start_time
                    
                    if https_response.status_code == 200:
                        https_data = https_response.json()
                        print(f"  ✅ HTTPS测试成功!")
                        print(f"    代理IP: {https_data.get('origin')}")
                        print(f"    响应时间: {https_response_time:.2f}秒")
                        
                        # 测试YouTube访问
                        test_youtube_access(proxies)
                        
                        return {
                            "success": True,
                            "endpoint": endpoint,
                            "proxy_url": proxy_url,
                            "proxy_ip": data.get('origin')
                        }
                    else:
                        print(f"  ❌ HTTPS测试失败 - 状态码: {https_response.status_code}")
                        
                except Exception as e:
                    print(f"  ❌ HTTPS测试异常: {str(e)[:100]}...")
                
            else:
                print(f"  ❌ HTTP测试失败 - 状态码: {response.status_code}")
                
        except requests.exceptions.ProxyError as e:
            print(f"  ❌ 代理错误: {str(e)[:100]}...")
        except requests.exceptions.Timeout:
            print(f"  ❌ 连接超时")
        except requests.exceptions.ConnectionError as e:
            print(f"  ❌ 连接错误: {str(e)[:100]}...")
        except Exception as e:
            print(f"  ❌ 其他错误: {str(e)[:100]}...")
        
        time.sleep(3)  # 避免请求过快
    
    return {"success": False}

def test_youtube_access(proxies):
    """测试通过代理访问YouTube"""
    print("  🎯 测试YouTube访问...")
    
    try:
        response = requests.get(
            "https://www.youtube.com",
            proxies=proxies,
            timeout=25,
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }
        )
        
        if response.status_code == 200:
            if "YouTube" in response.text:
                print("    ✅ YouTube访问成功!")
            else:
                print("    ⚠️  YouTube访问成功但内容异常")
        else:
            print(f"    ❌ YouTube访问失败 - 状态码: {response.status_code}")
            
    except Exception as e:
        print(f"    ❌ YouTube访问异常: {str(e)[:80]}...")

def update_config_files(working_config):
    """更新配置文件"""
    if not working_config["success"]:
        return
    
    print(f"\n📝 更新配置文件...")
    
    # 更新.env文件
    env_content = f"""# Webshare.io 正确配置
# 更新时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
# 用户名: gorkpiln (不是gorkpiln-1到gorkpiln-10)

# Webshare 代理认证信息
WEBSHARE_USERNAME=gorkpiln
WEBSHARE_PASSWORD=6lwffvf16jjn
WEBSHARE_ENDPOINT={working_config["endpoint"]}

# 代理配置
PROXY_USERNAME=gorkpiln
PROXY_PASSWORD=6lwffvf16jjn
ENABLE_PROXY=true
PROXY_TIMEOUT=25

# 爬虫配置 - 针对住宅代理优化
MAX_CHANNELS_PER_RUN=50
REQUEST_DELAY_MIN=4
REQUEST_DELAY_MAX=8
CONCURRENT_REQUESTS=2

# 日志级别
LOG_LEVEL=INFO

# 数据库配置
DATABASE_URL=sqlite:///youtube_channels.db
"""
    
    with open(".env", "w", encoding="utf-8") as f:
        f.write(env_content)
    
    print("  ✅ .env 文件已更新")
    print(f"  ✅ 工作代理: {working_config['proxy_url']}")
    print(f"  ✅ 代理IP: {working_config['proxy_ip']}")

def main():
    """主函数"""
    print("🔧 Webshare代理正确配置测试")
    print("基于API返回的正确用户名: gorkpiln")
    print()
    
    # 测试代理
    result = test_webshare_proxy()
    
    print("\n" + "=" * 50)
    print("📊 测试总结:")
    
    if result["success"]:
        print("🎉 代理测试成功!")
        print(f"✅ 工作端点: {result['endpoint']}")
        print(f"✅ 代理IP: {result['proxy_ip']}")
        
        # 更新配置文件
        update_config_files(result)
        
        print("\n📝 下一步:")
        print("1. 运行: python switch_config.py residential")
        print("2. 测试: python test_v1.py")
        print("3. 开始爬取: python main.py --mode once")
        
    else:
        print("❌ 所有代理端点都失败了")
        print("\n🔍 可能的原因:")
        print("1. VPN仍在干扰连接")
        print("2. 网络防火墙限制")
        print("3. 代理服务器维护")
        print("\n💡 建议:")
        print("1. 关闭VPN重新测试")
        print("2. 尝试不同网络环境")
        print("3. 先使用无代理模式: python test_no_proxy.py")

if __name__ == '__main__':
    main()
