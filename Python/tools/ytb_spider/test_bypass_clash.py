#!/usr/bin/env python3
"""
绕过Clash系统代理测试Webshare
通过设置NO_PROXY环境变量绕过系统代理

作者: GreenJoson
创建时间: 2025-06-26
"""

import os
import requests
import time

def test_webshare_bypass_clash():
    """绕过Clash测试Webshare代理"""
    print("🔧 绕过Clash系统代理测试Webshare")
    print("=" * 50)
    
    # 设置NO_PROXY环境变量，绕过系统代理
    webshare_domains = [
        "webshare.io",
        "p.webshare.io", 
        "proxy.webshare.io",
        "rotating-residential.webshare.io",
        "httpbin.org",
        "ipify.org",
        "ip-api.com"
    ]
    
    # 保存原始环境变量
    original_no_proxy = os.environ.get('NO_PROXY', '')
    original_http_proxy = os.environ.get('HTTP_PROXY', '')
    original_https_proxy = os.environ.get('HTTPS_PROXY', '')
    
    try:
        # 设置绕过代理的域名
        os.environ['NO_PROXY'] = ','.join(webshare_domains)
        
        # 清除系统代理设置
        if 'HTTP_PROXY' in os.environ:
            del os.environ['HTTP_PROXY']
        if 'HTTPS_PROXY' in os.environ:
            del os.environ['HTTPS_PROXY']
        if 'http_proxy' in os.environ:
            del os.environ['http_proxy']
        if 'https_proxy' in os.environ:
            del os.environ['https_proxy']
        
        print("✅ 已设置绕过Clash代理")
        print(f"   NO_PROXY: {os.environ['NO_PROXY']}")
        
        # 测试Webshare代理
        username = "gorkpiln"
        password = "6lwffvf16jjn"
        
        test_endpoints = [
            "p.webshare.io:80",
            "proxy.webshare.io:80"
        ]
        
        for endpoint in test_endpoints:
            print(f"\n🧪 测试端点: {endpoint}")
            
            proxy_url = f"http://{username}:{password}@{endpoint}"
            proxies = {
                "http": proxy_url,
                "https": proxy_url
            }
            
            try:
                # 创建新的session，确保不使用系统代理
                session = requests.Session()
                session.trust_env = False  # 不信任环境变量中的代理设置
                session.proxies = proxies
                
                response = session.get(
                    "http://httpbin.org/ip",
                    timeout=20,
                    headers={'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    proxy_ip = data.get('origin')
                    print(f"  ✅ 连接成功!")
                    print(f"     代理IP: {proxy_ip}")
                    
                    # 测试HTTPS
                    https_response = session.get(
                        "https://httpbin.org/ip",
                        timeout=20,
                        headers={'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'}
                    )
                    
                    if https_response.status_code == 200:
                        print(f"  ✅ HTTPS也正常!")
                        
                        return {
                            "success": True,
                            "endpoint": endpoint,
                            "proxy_ip": proxy_ip,
                            "proxy_url": proxy_url
                        }
                    else:
                        print(f"  ❌ HTTPS失败: {https_response.status_code}")
                else:
                    print(f"  ❌ 连接失败: {response.status_code}")
                    
            except Exception as e:
                print(f"  ❌ 连接异常: {str(e)[:100]}...")
            
            time.sleep(2)
    
    finally:
        # 恢复原始环境变量
        if original_no_proxy:
            os.environ['NO_PROXY'] = original_no_proxy
        elif 'NO_PROXY' in os.environ:
            del os.environ['NO_PROXY']
            
        if original_http_proxy:
            os.environ['HTTP_PROXY'] = original_http_proxy
        if original_https_proxy:
            os.environ['HTTPS_PROXY'] = original_https_proxy
    
    return {"success": False}

def test_direct_connection():
    """测试直接连接（完全绕过所有代理）"""
    print("\n🎯 测试直接连接（无代理）...")
    
    # 完全清除代理环境变量
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY']
    original_values = {}
    
    for var in proxy_vars:
        original_values[var] = os.environ.get(var)
        if var in os.environ:
            del os.environ[var]
    
    try:
        session = requests.Session()
        session.trust_env = False
        session.proxies = {}  # 明确设置为无代理
        
        response = session.get(
            "https://httpbin.org/ip",
            timeout=15,
            headers={'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ 直接连接成功!")
            print(f"     真实IP: {data.get('origin')}")
            return True
        else:
            print(f"  ❌ 直接连接失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ 直接连接异常: {str(e)[:100]}...")
        return False
    
    finally:
        # 恢复环境变量
        for var, value in original_values.items():
            if value:
                os.environ[var] = value

def main():
    """主函数"""
    print("🚀 Clash冲突解决测试")
    print("检测到您使用Clash Verge全局代理")
    print("这可能导致与Webshare代理冲突")
    print()
    
    # 1. 测试直接连接
    direct_ok = test_direct_connection()
    
    if not direct_ok:
        print("\n❌ 连直接连接都失败了，可能是网络问题")
        return
    
    # 2. 测试绕过Clash的Webshare连接
    result = test_webshare_bypass_clash()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    
    if result["success"]:
        print("🎉 成功绕过Clash连接Webshare!")
        print(f"✅ 工作端点: {result['endpoint']}")
        print(f"✅ 代理IP: {result['proxy_ip']}")
        
        print(f"\n📝 解决方案:")
        print("1. 在Clash中添加Webshare域名直连规则")
        print("2. 或者在运行爬虫时临时关闭Clash系统代理")
        print("3. 或者使用以下命令运行爬虫:")
        print(f"   NO_PROXY='webshare.io,p.webshare.io,httpbin.org' python main.py")
        
    else:
        print("❌ 即使绕过Clash也无法连接Webshare")
        print("\n🔍 可能的原因:")
        print("1. ISP阻止了代理连接")
        print("2. 网络防火墙限制")
        print("3. Webshare服务器问题")
        
        print(f"\n💡 建议:")
        print("1. 尝试手机热点网络")
        print("2. 联系ISP确认代理政策")
        print("3. 先使用无代理模式开发")
        print("4. 考虑使用其他代理服务")

if __name__ == '__main__':
    main()
