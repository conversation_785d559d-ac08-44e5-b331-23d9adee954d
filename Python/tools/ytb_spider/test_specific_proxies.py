#!/usr/bin/env python3
"""
测试具体的Webshare代理列表
使用从Google Colab获取的具体代理信息

作者: GreenJoson
创建时间: 2025-06-26
"""

import requests
import time
import random

def test_specific_proxies():
    """测试具体的代理列表"""
    print("🔍 测试具体的Webshare代理")
    print("=" * 60)

    # 生成完整的代理列表 (1-100)
    # 为了快速测试，先测试前10个，如果成功再测试更多
    test_count = 10  # 可以调整测试数量

    proxy_list = []
    for i in range(1, test_count + 1):
        proxy_list.append(f"gorkpiln-au-ca-gb-us-{i}:<EMAIL>:80")

    print(f"准备测试前 {test_count} 个代理 (总共有1-100可用)")
    print(f"如果测试成功，可以增加test_count来测试更多代理")

    working_proxies = []

    for i, proxy_auth in enumerate(proxy_list, 1):
        print(f"\n[{i}/{len(proxy_list)}] 测试代理: {proxy_auth}")

        proxy_url = f"http://{proxy_auth}"
        proxies = {
            "http": proxy_url,
            "https": proxy_url
        }

        try:
            start_time = time.time()

            # 测试IP检测
            response = requests.get(
                "http://httpbin.org/ip",
                proxies=proxies,
                timeout=20,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
                }
            )

            response_time = time.time() - start_time

            if response.status_code == 200:
                data = response.json()
                proxy_ip = data.get('origin')

                print(f"  ✅ 基础连接成功!")
                print(f"    代理IP: {proxy_ip}")
                print(f"    响应时间: {response_time:.2f}秒")

                # 获取地理位置信息
                try:
                    geo_response = requests.get(
                        f"http://ip-api.com/json/{proxy_ip}",
                        timeout=10
                    )

                    if geo_response.status_code == 200:
                        geo_data = geo_response.json()
                        country = geo_data.get('country', 'Unknown')
                        city = geo_data.get('city', 'Unknown')
                        country_code = geo_data.get('countryCode', '')

                        print(f"    位置: {city}, {country} ({country_code})")

                        # 检查是否为英文地区
                        if country_code in ['US', 'GB', 'CA', 'AU']:
                            print(f"    🎯 确认为英文地区IP!")
                        else:
                            print(f"    ⚠️  非英文地区IP")

                except:
                    print(f"    ⚠️  无法获取地理位置信息")

                # 测试HTTPS
                try:
                    https_response = requests.get(
                        "https://httpbin.org/ip",
                        proxies=proxies,
                        timeout=15,
                        headers={'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'}
                    )

                    if https_response.status_code == 200:
                        print(f"    ✅ HTTPS连接也正常!")

                        working_proxies.append({
                            "auth": proxy_auth,
                            "ip": proxy_ip,
                            "response_time": response_time,
                            "location": f"{city}, {country}" if 'city' in locals() else "Unknown"
                        })

                    else:
                        print(f"    ❌ HTTPS连接失败: {https_response.status_code}")

                except Exception as e:
                    print(f"    ❌ HTTPS测试异常: {str(e)[:60]}...")

            else:
                print(f"  ❌ 连接失败 - 状态码: {response.status_code}")

        except requests.exceptions.ProxyError as e:
            print(f"  ❌ 代理错误: {str(e)[:80]}...")
        except requests.exceptions.Timeout:
            print(f"  ❌ 连接超时")
        except requests.exceptions.ConnectionError as e:
            print(f"  ❌ 连接错误: {str(e)[:80]}...")
        except Exception as e:
            print(f"  ❌ 其他错误: {str(e)[:80]}...")

        # 避免请求过快
        time.sleep(2)

    return working_proxies

def test_youtube_access(working_proxies):
    """测试YouTube访问"""
    if not working_proxies:
        return False

    print(f"\n🎯 测试YouTube访问...")

    # 选择最快的代理
    best_proxy = min(working_proxies, key=lambda x: x['response_time'])

    proxy_url = f"http://{best_proxy['auth']}"
    proxies = {
        "http": proxy_url,
        "https": proxy_url
    }

    print(f"使用代理: {best_proxy['ip']} ({best_proxy['location']})")

    # 测试YouTube频道访问
    test_channels = [
        "https://www.youtube.com/@MrBeast",
        "https://www.youtube.com/@PewDiePie"
    ]

    youtube_success = 0
    twitter_found = 0

    for channel_url in test_channels:
        try:
            print(f"\n  测试频道: {channel_url}")

            response = requests.get(
                channel_url,
                proxies=proxies,
                timeout=25,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                }
            )

            if response.status_code == 200:
                print(f"    ✅ 访问成功 - 页面大小: {len(response.text):,}")
                youtube_success += 1

                # 检查Twitter链接
                content = response.text.lower()
                if 'twitter.com' in content or 'x.com' in content:
                    print(f"    🐦 发现Twitter链接!")
                    twitter_found += 1

                    # 提取具体的Twitter链接
                    import re
                    twitter_patterns = [
                        r'https?://(?:www\.)?twitter\.com/[a-zA-Z0-9_]+',
                        r'https?://(?:www\.)?x\.com/[a-zA-Z0-9_]+',
                    ]

                    found_links = set()
                    for pattern in twitter_patterns:
                        matches = re.findall(pattern, response.text, re.IGNORECASE)
                        found_links.update(matches)

                    if found_links:
                        print(f"    📋 Twitter链接:")
                        for link in list(found_links)[:3]:  # 只显示前3个
                            clean_link = link.split('?')[0].split('#')[0]
                            print(f"      - {clean_link}")
                else:
                    print(f"    ⚠️  未发现Twitter链接")

            else:
                print(f"    ❌ 访问失败 - 状态码: {response.status_code}")

        except Exception as e:
            print(f"    ❌ 访问异常: {str(e)[:60]}...")

        time.sleep(3)

    success_rate = youtube_success / len(test_channels) * 100
    twitter_rate = twitter_found / youtube_success * 100 if youtube_success > 0 else 0

    print(f"\n📊 YouTube测试结果:")
    print(f"  成功访问: {youtube_success}/{len(test_channels)} ({success_rate:.1f}%)")
    print(f"  发现Twitter: {twitter_found}/{youtube_success} ({twitter_rate:.1f}%)")

    return youtube_success > 0

def update_proxy_config(working_proxies):
    """更新代理配置文件"""
    if not working_proxies:
        return

    print(f"\n📝 更新代理配置文件...")

    # 按响应时间排序
    working_proxies.sort(key=lambda x: x['response_time'])

    # 更新proxy_list.txt
    proxy_content = f"""# Webshare.io 英文地区代理配置 (已验证)
# 更新时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
# 用户名格式: gorkpiln-au-ca-gb-us-数字
# 密码: 6lwffvf16jjn
# 验证状态: ✅ 已测试可用

# 已验证的工作代理 (按响应时间排序):
"""

    for i, proxy in enumerate(working_proxies, 1):
        proxy_content += f"{proxy['auth']}  # {proxy['location']} - {proxy['response_time']:.2f}s\n"

    with open("proxy_list.txt", "w", encoding="utf-8") as f:
        f.write(proxy_content)

    print(f"  ✅ 已更新 proxy_list.txt，包含 {len(working_proxies)} 个工作代理")

    # 更新.env文件，使用最快的代理
    best_proxy = working_proxies[0]
    username = best_proxy['auth'].split(':')[0]

    env_content = f"""# Webshare.io 英文地区代理配置 (已验证)
# 更新时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
# 最佳代理: {best_proxy['ip']} ({best_proxy['location']})

# Webshare 代理认证信息
WEBSHARE_USERNAME={username}
WEBSHARE_PASSWORD=6lwffvf16jjn
WEBSHARE_ENDPOINT=p.webshare.io:80

# 代理配置
PROXY_USERNAME={username}
PROXY_PASSWORD=6lwffvf16jjn
ENABLE_PROXY=true
PROXY_TIMEOUT=25

# 爬虫配置
MAX_CHANNELS_PER_RUN=50
REQUEST_DELAY_MIN=4
REQUEST_DELAY_MAX=8
CONCURRENT_REQUESTS=2

# 日志级别
LOG_LEVEL=INFO

# 数据库配置
DATABASE_URL=sqlite:///youtube_channels.db
"""

    with open(".env", "w", encoding="utf-8") as f:
        f.write(env_content)

    print(f"  ✅ 已更新 .env 文件")

def main():
    """主函数"""
    print("🚀 Webshare具体代理测试")
    print("使用从Google Colab获取的代理列表")
    print()

    # 1. 测试具体代理
    working_proxies = test_specific_proxies()

    print(f"\n" + "=" * 60)
    print(f"📊 代理测试总结:")

    if working_proxies:
        print(f"✅ 找到 {len(working_proxies)} 个工作代理!")

        # 显示工作代理信息
        print(f"\n🎯 工作代理列表:")
        for i, proxy in enumerate(working_proxies, 1):
            print(f"  {i}. {proxy['ip']} - {proxy['location']} ({proxy['response_time']:.2f}s)")

        # 2. 测试YouTube访问
        youtube_ok = test_youtube_access(working_proxies)

        if youtube_ok:
            print(f"\n🎉 代理配置完全成功!")

            # 3. 更新配置文件
            update_proxy_config(working_proxies)

            print(f"\n📝 下一步:")
            print(f"1. 运行爬虫: python main.py --mode once")
            print(f"2. 测试Twitter映射: python test_twitter_mapping.py")
            print(f"3. 开始大规模爬取")

        else:
            print(f"\n⚠️  代理可用但YouTube访问有问题")
            print(f"💡 建议先用无代理模式测试爬虫逻辑")
    else:
        print(f"❌ 所有代理都无法连接")
        print(f"\n💡 可能的原因:")
        print(f"1. 网络环境限制")
        print(f"2. 代理服务器维护")
        print(f"3. 认证信息问题")
        print(f"\n建议:")
        print(f"1. 检查网络连接")
        print(f"2. 尝试不同时间段")
        print(f"3. 先用无代理模式: python test_no_proxy.py")

if __name__ == '__main__':
    main()
