#!/usr/bin/env python3
"""
Webshare API管理脚本
使用API Key管理代理、获取代理列表、检查账户状态等

作者: GreenJoson
创建时间: 2025-06-26
"""

import requests
import json
import time
from typing import Dict, List, Optional

class WebshareAPI:
    """Webshare API客户端"""
    
    def __init__(self, api_key: str):
        """
        初始化API客户端
        
        Args:
            api_key: Webshare API密钥
        """
        self.api_key = api_key
        self.base_url = "https://proxy.webshare.io/api/v2"
        self.headers = {
            "Authorization": f"Token {api_key}",
            "Content-Type": "application/json"
        }
    
    def get_account_info(self) -> Dict:
        """
        获取账户信息
        
        Returns:
            Dict: 账户信息
        """
        try:
            response = requests.get(
                f"{self.base_url}/profile/",
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                return {"success": True, "data": response.json()}
            else:
                return {"success": False, "error": f"HTTP {response.status_code}", "details": response.text}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_proxy_list(self, limit: int = 100) -> Dict:
        """
        获取代理列表
        
        Args:
            limit: 返回的代理数量限制
            
        Returns:
            Dict: 代理列表
        """
        try:
            response = requests.get(
                f"{self.base_url}/proxy/list/",
                headers=self.headers,
                params={"page_size": limit},
                timeout=15
            )
            
            if response.status_code == 200:
                return {"success": True, "data": response.json()}
            else:
                return {"success": False, "error": f"HTTP {response.status_code}", "details": response.text}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_proxy_config(self) -> Dict:
        """
        获取代理配置信息
        
        Returns:
            Dict: 代理配置
        """
        try:
            response = requests.get(
                f"{self.base_url}/proxy/config/",
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                return {"success": True, "data": response.json()}
            else:
                return {"success": False, "error": f"HTTP {response.status_code}", "details": response.text}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def test_proxy_connection(self, proxy_info: Dict) -> Dict:
        """
        测试单个代理连接
        
        Args:
            proxy_info: 代理信息字典
            
        Returns:
            Dict: 测试结果
        """
        try:
            proxy_url = f"http://{proxy_info['username']}:{proxy_info['password']}@{proxy_info['proxy_address']}:{proxy_info['port']}"
            
            proxies = {
                "http": proxy_url,
                "https": proxy_url
            }
            
            start_time = time.time()
            response = requests.get(
                "https://httpbin.org/ip",
                proxies=proxies,
                timeout=15,
                headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            )
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                ip_data = response.json()
                return {
                    "success": True,
                    "proxy_ip": ip_data.get("origin"),
                    "response_time": response_time,
                    "proxy_url": proxy_url
                }
            else:
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}",
                    "proxy_url": proxy_url
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "proxy_url": proxy_url if 'proxy_url' in locals() else "N/A"
            }


def main():
    """主函数 - 测试Webshare API功能"""
    print("🔧 Webshare API测试工具")
    print("=" * 60)
    
    # 初始化API客户端
    api_key = "ulul4v9sr44nvbd2a0v3yhyzpzs2dfgvkdcsnoyg"
    client = WebshareAPI(api_key)
    
    # 1. 测试账户信息
    print("👤 获取账户信息...")
    account_info = client.get_account_info()
    
    if account_info["success"]:
        data = account_info["data"]
        print("  ✅ 账户信息获取成功")
        print(f"    用户名: {data.get('username', 'N/A')}")
        print(f"    邮箱: {data.get('email', 'N/A')}")
        print(f"    账户类型: {data.get('account_type', 'N/A')}")
    else:
        print(f"  ❌ 账户信息获取失败: {account_info['error']}")
        return
    
    # 2. 获取代理配置
    print("\n⚙️ 获取代理配置...")
    config_info = client.get_proxy_config()
    
    if config_info["success"]:
        print("  ✅ 代理配置获取成功")
        config_data = config_info["data"]
        print(f"    配置详情: {json.dumps(config_data, indent=2)}")
    else:
        print(f"  ❌ 代理配置获取失败: {config_info['error']}")
    
    # 3. 获取代理列表
    print("\n📋 获取代理列表...")
    proxy_list = client.get_proxy_list(limit=10)
    
    if proxy_list["success"]:
        data = proxy_list["data"]
        proxies = data.get("results", [])
        
        print(f"  ✅ 代理列表获取成功 - 总数: {data.get('count', 0)}")
        print(f"    显示前 {len(proxies)} 个代理:")
        
        for i, proxy in enumerate(proxies[:5], 1):
            print(f"    [{i}] {proxy.get('proxy_address')}:{proxy.get('port')}")
            print(f"        用户名: {proxy.get('username')}")
            print(f"        密码: {proxy.get('password')}")
            print(f"        类型: {proxy.get('proxy_type', 'N/A')}")
            print()
        
        # 4. 测试前3个代理连接
        print("🔍 测试代理连接...")
        working_proxies = []
        
        for i, proxy in enumerate(proxies[:3], 1):
            print(f"  [{i}/3] 测试代理: {proxy.get('proxy_address')}:{proxy.get('port')}")
            
            test_result = client.test_proxy_connection(proxy)
            
            if test_result["success"]:
                print(f"    ✅ 连接成功 - 代理IP: {test_result['proxy_ip']}")
                print(f"    响应时间: {test_result['response_time']:.2f}秒")
                working_proxies.append({
                    "proxy": proxy,
                    "test_result": test_result
                })
            else:
                print(f"    ❌ 连接失败: {test_result['error']}")
            
            time.sleep(2)  # 避免请求过快
        
        # 5. 生成配置文件
        if working_proxies:
            print(f"\n📝 生成配置文件 - 找到 {len(working_proxies)} 个可用代理")
            
            # 更新proxy_list.txt
            with open("proxy_list.txt", "w", encoding="utf-8") as f:
                f.write("# Webshare.io API获取的代理列表\n")
                f.write(f"# 生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# API Key: {api_key[:10]}...\n")
                f.write(f"# 总代理数: {data.get('count', 0)}\n\n")
                
                for proxy in proxies:
                    proxy_line = f"{proxy.get('username')}:{proxy.get('password')}@{proxy.get('proxy_address')}:{proxy.get('port')}"
                    f.write(f"{proxy_line}\n")
            
            print("  ✅ proxy_list.txt 已更新")
            
            # 更新.env文件
            env_content = f"""# Webshare.io API配置
# 更新时间: {time.strftime('%Y-%m-%d %H:%M:%S')}

# API配置
WEBSHARE_API_KEY={api_key}

# 第一个可用代理配置
WEBSHARE_USERNAME={working_proxies[0]['proxy']['username']}
WEBSHARE_PASSWORD={working_proxies[0]['proxy']['password']}
WEBSHARE_ENDPOINT={working_proxies[0]['proxy']['proxy_address']}:{working_proxies[0]['proxy']['port']}

# 代理配置
PROXY_USERNAME={working_proxies[0]['proxy']['username']}
PROXY_PASSWORD={working_proxies[0]['proxy']['password']}
ENABLE_PROXY=true
PROXY_TIMEOUT=20

# 爬虫配置 - 针对住宅代理优化
MAX_CHANNELS_PER_RUN=50
REQUEST_DELAY_MIN=3
REQUEST_DELAY_MAX=6
CONCURRENT_REQUESTS=2

# 日志级别
LOG_LEVEL=INFO

# 数据库配置
DATABASE_URL=sqlite:///youtube_channels.db
"""
            
            with open(".env", "w", encoding="utf-8") as f:
                f.write(env_content)
            
            print("  ✅ .env 文件已更新")
            
        else:
            print("\n❌ 没有找到可用的代理")
    
    else:
        print(f"  ❌ 代理列表获取失败: {proxy_list['error']}")
    
    print("\n" + "=" * 60)
    print("📊 API测试总结:")
    
    if account_info["success"] and proxy_list["success"]:
        print("✅ Webshare API连接正常")
        print("✅ 账户状态正常")
        print("✅ 代理列表获取成功")
        
        if 'working_proxies' in locals() and working_proxies:
            print(f"✅ 找到 {len(working_proxies)} 个可用代理")
            print("\n📝 下一步:")
            print("1. 运行: python switch_config.py residential")
            print("2. 测试: python test_v1.py")
            print("3. 开始爬取: python main.py --mode once")
        else:
            print("⚠️  代理连接测试失败")
            print("建议检查网络环境或VPN设置")
    else:
        print("❌ API连接或认证失败")
        print("请检查API Key是否正确")


if __name__ == '__main__':
    main()
