#!/usr/bin/env python3
"""
YouTube Twitter爬虫主程序
自动发现YouTube频道并提取其中的Twitter信息

功能特点:
1. 自动发现YouTube频道
2. 提取频道中的Twitter/X链接
3. 使用代理IP防止被封
4. 数据存储到SQLite数据库
5. 支持无限循环运行
6. 完整的日志记录

作者: GreenJoson
创建时间: 2025-06-26
版本: v1.0
"""

import sys
import time
import signal
import argparse
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加项目路径到Python路径
sys.path.append(str(Path(__file__).parent))

from config import LOG_CONFIG, SPIDER_CONFIG, create_directories, load_env_config
from database import DatabaseManager
from spider import YouTubeSpider
from proxy_manager import ProxyManager


class YouTubeTwitterCrawler:
    """YouTube Twitter爬虫主控制器"""

    def __init__(self):
        """初始化爬虫控制器"""
        self.setup_logging()
        self.setup_signal_handlers()

        # 创建必要目录
        create_directories()

        # 加载环境配置
        self.env_config = load_env_config()

        # 初始化组件
        self.db = DatabaseManager()
        self.spider = YouTubeSpider()
        self.proxy_manager = ProxyManager()

        # 运行状态
        self.is_running = True
        self.total_crawled = 0
        self.total_twitter_found = 0

        logger.info("YouTube Twitter爬虫初始化完成")

    def setup_logging(self):
        """设置日志配置"""
        logger.remove()  # 移除默认处理器

        # 控制台日志
        logger.add(
            sys.stdout,
            format=LOG_CONFIG['format'],
            level=LOG_CONFIG['level'],
            colorize=True
        )

        # 文件日志
        LOG_CONFIG['log_file'].parent.mkdir(exist_ok=True)
        logger.add(
            LOG_CONFIG['log_file'],
            format=LOG_CONFIG['format'],
            level=LOG_CONFIG['level'],
            rotation=LOG_CONFIG['rotation'],
            retention=LOG_CONFIG['retention'],
            encoding='utf-8'
        )

        logger.info("日志系统初始化完成")

    def setup_signal_handlers(self):
        """设置信号处理器，支持优雅退出"""
        def signal_handler(signum, frame):
            logger.info(f"接收到信号 {signum}，准备退出...")
            self.is_running = False

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    def run_discovery_cycle(self) -> int:
        """
        运行一轮频道发现

        Returns:
            int: 发现的新频道数量
        """
        try:
            logger.info("开始频道发现周期")

            # 发现新频道
            discovered_channels = self.spider.discover_channels(
                max_channels=SPIDER_CONFIG['max_channels_per_run']
            )

            new_channels = 0
            for channel_url in discovered_channels:
                if not self.is_running:
                    break

                # 检查是否已存在
                from utils import URLValidator
                channel_id = URLValidator.extract_channel_id(channel_url)
                if channel_id and self.db.get_channel_by_id(channel_id):
                    continue

                # 添加到待爬取列表
                channel_data = {
                    'channel_id': channel_id or channel_url,
                    'channel_url': channel_url,
                    'crawl_status': 'pending'
                }
                self.db.insert_channel(channel_data)
                new_channels += 1

            logger.info(f"发现周期完成，新增 {new_channels} 个待爬取频道")
            return new_channels

        except Exception as e:
            logger.error(f"频道发现周期异常: {e}")
            return 0

    def run_crawl_cycle(self) -> int:
        """
        运行一轮频道爬取

        Returns:
            int: 成功爬取的频道数量
        """
        try:
            logger.info("开始频道爬取周期")

            # 获取待爬取频道
            pending_channels = self.db.get_pending_channels(
                limit=SPIDER_CONFIG['max_channels_per_run']
            )

            if not pending_channels:
                logger.info("没有待爬取的频道")
                return 0

            crawled_count = 0
            twitter_found_count = 0

            for channel_id in pending_channels:
                if not self.is_running:
                    break

                try:
                    # 获取频道信息
                    channel_info = self.db.get_channel_by_id(channel_id)
                    if not channel_info:
                        continue

                    channel_url = channel_info['channel_url']

                    # 爬取频道
                    logger.info(f"爬取频道: {channel_url}")
                    channel_data = self.spider.crawl_channel(channel_url)

                    if channel_data:
                        crawled_count += 1
                        self.total_crawled += 1

                        # 检查是否找到Twitter链接
                        if channel_data.get('twitter_links'):
                            twitter_found_count += 1
                            self.total_twitter_found += 1
                            logger.success(
                                f"发现Twitter链接! 频道: {channel_data.get('channel_name')}, "
                                f"Twitter: {channel_data['twitter_links']}"
                            )

                    # 请求间隔
                    if self.is_running:
                        time.sleep(2)

                except Exception as e:
                    logger.error(f"爬取频道失败: {channel_id}, 错误: {e}")
                    continue

            logger.info(
                f"爬取周期完成，成功爬取 {crawled_count} 个频道，"
                f"发现 {twitter_found_count} 个包含Twitter的频道"
            )
            return crawled_count

        except Exception as e:
            logger.error(f"频道爬取周期异常: {e}")
            return 0

    def print_statistics(self):
        """打印统计信息"""
        try:
            db_stats = self.db.get_statistics()
            proxy_stats = self.proxy_manager.get_proxy_stats()

            logger.info("=" * 60)
            logger.info("运行统计信息:")
            logger.info(f"  总爬取频道数: {self.total_crawled}")
            logger.info(f"  发现Twitter频道数: {self.total_twitter_found}")
            logger.info(f"  数据库总频道数: {db_stats.get('total_channels', 0)}")
            logger.info(f"  数据库Twitter频道数: {db_stats.get('twitter_channels', 0)}")
            logger.info(f"  Twitter发现率: {db_stats.get('twitter_ratio', 0):.2%}")
            logger.info(f"  代理总数: {proxy_stats.get('total_proxies', 0)}")
            logger.info(f"  可用代理数: {proxy_stats.get('available_proxies', 0)}")
            logger.info("=" * 60)

        except Exception as e:
            logger.error(f"打印统计信息失败: {e}")

    def run_continuous(self, discovery_interval: int = 3600, crawl_interval: int = 300):
        """
        持续运行爬虫

        Args:
            discovery_interval: 频道发现间隔（秒）
            crawl_interval: 频道爬取间隔（秒）
        """
        logger.info("开始持续运行模式")
        logger.info(f"频道发现间隔: {discovery_interval}秒")
        logger.info(f"频道爬取间隔: {crawl_interval}秒")

        last_discovery_time = 0
        last_stats_time = 0

        while self.is_running:
            try:
                current_time = time.time()

                # 频道发现
                if current_time - last_discovery_time >= discovery_interval:
                    self.run_discovery_cycle()
                    last_discovery_time = current_time

                # 频道爬取
                crawled = self.run_crawl_cycle()

                # 打印统计信息（每小时一次）
                if current_time - last_stats_time >= 3600:
                    self.print_statistics()
                    last_stats_time = current_time

                # 如果没有爬取到任何频道，等待更长时间
                if crawled == 0:
                    logger.info(f"没有可爬取的频道，等待 {crawl_interval} 秒...")
                    time.sleep(crawl_interval)
                else:
                    time.sleep(10)  # 短暂休息

            except KeyboardInterrupt:
                logger.info("接收到中断信号，准备退出...")
                break
            except Exception as e:
                logger.error(f"运行异常: {e}")
                time.sleep(60)  # 异常后等待1分钟

        logger.info("爬虫已停止运行")

    def run_once(self):
        """运行一次完整的爬取周期"""
        logger.info("开始单次运行模式")

        # 频道发现
        discovered = self.run_discovery_cycle()

        # 频道爬取
        crawled = self.run_crawl_cycle()

        # 打印统计信息
        self.print_statistics()

        logger.info(f"单次运行完成，发现 {discovered} 个新频道，爬取 {crawled} 个频道")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='YouTube Twitter爬虫')
    parser.add_argument('--mode', choices=['once', 'continuous'], default='continuous',
                       help='运行模式: once=单次运行, continuous=持续运行')
    parser.add_argument('--discovery-interval', type=int, default=3600,
                       help='频道发现间隔（秒），默认3600秒')
    parser.add_argument('--crawl-interval', type=int, default=300,
                       help='频道爬取间隔（秒），默认300秒')

    args = parser.parse_args()

    try:
        crawler = YouTubeTwitterCrawler()

        if args.mode == 'once':
            crawler.run_once()
        else:
            crawler.run_continuous(args.discovery_interval, args.crawl_interval)

    except Exception as e:
        logger.error(f"程序运行异常: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
