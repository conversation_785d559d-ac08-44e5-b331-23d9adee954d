#!/usr/bin/env python3
"""
无代理模式测试脚本
测试基本的YouTube访问功能

作者: GreenJoson
创建时间: 2025-06-26
"""

import requests
import time
import json
from config_no_proxy import get_config

def test_basic_connection():
    """测试基本网络连接"""
    print("🌐 测试基本网络连接...")
    
    test_urls = [
        "https://www.google.com",
        "https://httpbin.org/ip",
        "https://www.youtube.com"
    ]
    
    for url in test_urls:
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                print(f"  ✅ {url} - 连接成功")
            else:
                print(f"  ❌ {url} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"  ❌ {url} - 错误: {e}")

def test_youtube_access():
    """测试YouTube访问"""
    print("\n🎯 测试YouTube访问...")
    
    config = get_config()
    headers = {
        'User-Agent': config['crawler']['user_agents'][0],
        **config['crawler']['headers']
    }
    
    try:
        # 测试YouTube首页
        response = requests.get(
            "https://www.youtube.com",
            headers=headers,
            timeout=15
        )
        
        if response.status_code == 200:
            print("  ✅ YouTube首页访问成功")
            
            # 检查页面内容
            if "YouTube" in response.text:
                print("  ✅ 页面内容正常")
            else:
                print("  ⚠️  页面内容异常")
                
            # 检查是否被重定向
            if len(response.history) > 0:
                print(f"  ⚠️  发生了 {len(response.history)} 次重定向")
            else:
                print("  ✅ 无重定向")
                
        else:
            print(f"  ❌ YouTube访问失败 - 状态码: {response.status_code}")
            
    except Exception as e:
        print(f"  ❌ YouTube访问异常: {e}")

def test_youtube_search():
    """测试YouTube搜索功能"""
    print("\n🔍 测试YouTube搜索...")
    
    config = get_config()
    headers = {
        'User-Agent': config['crawler']['user_agents'][0],
        **config['crawler']['headers']
    }
    
    search_query = "python programming"
    search_url = f"https://www.youtube.com/results?search_query={search_query.replace(' ', '+')}"
    
    try:
        response = requests.get(
            search_url,
            headers=headers,
            timeout=15
        )
        
        if response.status_code == 200:
            print(f"  ✅ 搜索 '{search_query}' 成功")
            
            # 简单检查搜索结果
            if "videoRenderer" in response.text or "channelRenderer" in response.text:
                print("  ✅ 找到搜索结果")
            else:
                print("  ⚠️  未找到明显的搜索结果")
                
        else:
            print(f"  ❌ 搜索失败 - 状态码: {response.status_code}")
            
    except Exception as e:
        print(f"  ❌ 搜索异常: {e}")

def test_channel_access():
    """测试频道访问"""
    print("\n📺 测试频道访问...")
    
    config = get_config()
    headers = {
        'User-Agent': config['crawler']['user_agents'][0],
        **config['crawler']['headers']
    }
    
    # 测试一个知名频道
    test_channel = "UC_x5XG1OV2P6uZZ5FSM9Ttw"  # Google Developers
    channel_url = f"https://www.youtube.com/channel/{test_channel}"
    
    try:
        response = requests.get(
            channel_url,
            headers=headers,
            timeout=15
        )
        
        if response.status_code == 200:
            print(f"  ✅ 频道访问成功: {test_channel}")
            
            # 检查频道内容
            if "channelMetadataRenderer" in response.text or "c4TabbedHeaderRenderer" in response.text:
                print("  ✅ 频道数据正常")
            else:
                print("  ⚠️  频道数据可能异常")
                
        else:
            print(f"  ❌ 频道访问失败 - 状态码: {response.status_code}")
            
    except Exception as e:
        print(f"  ❌ 频道访问异常: {e}")

def test_rate_limiting():
    """测试请求频率限制"""
    print("\n⏱️ 测试请求频率...")
    
    config = get_config()
    headers = {
        'User-Agent': config['crawler']['user_agents'][0],
        **config['crawler']['headers']
    }
    
    test_url = "https://www.youtube.com"
    success_count = 0
    
    print("  发送5个连续请求...")
    
    for i in range(5):
        try:
            start_time = time.time()
            response = requests.get(test_url, headers=headers, timeout=10)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                success_count += 1
                print(f"    请求 {i+1}: ✅ 成功 ({response_time:.2f}秒)")
            else:
                print(f"    请求 {i+1}: ❌ 失败 - 状态码: {response.status_code}")
                
        except Exception as e:
            print(f"    请求 {i+1}: ❌ 异常 - {e}")
        
        # 添加延迟
        if i < 4:
            time.sleep(2)
    
    print(f"  成功率: {success_count}/5 ({success_count/5*100:.1f}%)")

def main():
    """主函数"""
    print("🧪 YouTube爬虫无代理模式测试")
    print("=" * 50)
    
    # 显示配置
    config = get_config()
    print(f"📁 工作目录: {config['base_dir']}")
    print(f"🌐 代理状态: {'禁用' if not config['proxy']['enabled'] else '启用'}")
    print()
    
    # 运行测试
    test_basic_connection()
    test_youtube_access()
    test_youtube_search()
    test_channel_access()
    test_rate_limiting()
    
    print("\n" + "=" * 50)
    print("📋 测试总结:")
    print("✅ 如果所有测试都通过，说明无代理模式可以正常工作")
    print("⚠️  如果有测试失败，可能需要调整请求频率或User-Agent")
    print()
    print("📝 下一步:")
    print("1. 如果测试通过，可以开始爬取数据")
    print("2. 解决代理问题后，切换回代理模式")
    print("3. 运行: python main.py --config config_no_proxy --mode once")

if __name__ == '__main__':
    main()
