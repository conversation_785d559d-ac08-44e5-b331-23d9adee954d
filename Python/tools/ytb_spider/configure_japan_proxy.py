#!/usr/bin/env python3
"""
配置Webshare代理使用日本IP
与用户的日本VPN保持一致

作者: GreenJoson
创建时间: 2025-06-26
"""

import requests
import json
import time

class WebshareJapanConfig:
    """Webshare日本代理配置器"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://proxy.webshare.io/api/v2"
        self.headers = {
            "Authorization": f"Token {api_key}",
            "Content-Type": "application/json"
        }
    
    def get_current_config(self):
        """获取当前代理配置"""
        try:
            response = requests.get(
                f"{self.base_url}/proxy/config/",
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                return {"success": True, "data": response.json()}
            else:
                return {"success": False, "error": f"HTTP {response.status_code}"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def set_japan_proxy(self):
        """设置日本代理"""
        try:
            # 更新配置为仅使用日本IP
            update_data = {
                "ip_authorization_country_codes": ["JP"]
            }
            
            response = requests.patch(
                f"{self.base_url}/proxy/config/",
                headers=self.headers,
                json=update_data,
                timeout=15
            )
            
            if response.status_code == 200:
                return {"success": True, "data": response.json()}
            else:
                return {"success": False, "error": f"HTTP {response.status_code}", "details": response.text}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def show_japan_ip_info(self, countries_data):
        """显示日本IP信息"""
        japan_ips = countries_data.get("JP", 0)
        total_ips = sum(countries_data.values())
        
        print("🇯🇵 日本代理IP信息:")
        print("=" * 50)
        print(f"日本IP数量: {japan_ips:,}")
        print(f"占总代理的: {japan_ips/total_ips*100:.2f}%")
        
        return japan_ips

def test_japan_proxy():
    """测试日本代理连接"""
    print("\n🧪 测试日本代理连接...")
    
    username = "gorkpiln"
    password = "6lwffvf16jjn"
    proxy_url = f"http://{username}:{password}@p.webshare.io:80"
    
    proxies = {
        "http": proxy_url,
        "https": proxy_url
    }
    
    test_results = []
    
    for i in range(3):
        try:
            print(f"  测试 {i+1}/3...")
            
            # 获取IP地理信息
            response = requests.get(
                "http://ip-api.com/json/",
                proxies=proxies,
                timeout=20,
                headers={'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'}
            )
            
            if response.status_code == 200:
                data = response.json()
                result = {
                    "ip": data.get("query"),
                    "country": data.get("country"),
                    "countryCode": data.get("countryCode"),
                    "city": data.get("city"),
                    "region": data.get("regionName"),
                    "timezone": data.get("timezone"),
                    "is_japan": data.get("countryCode") == "JP"
                }
                test_results.append(result)
                
                status = "✅" if result["is_japan"] else "⚠️"
                print(f"    {status} {result['ip']} - {result['city']}, {result['country']}")
                
            else:
                print(f"    ❌ 请求失败 - 状态码: {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ 测试异常: {str(e)[:60]}...")
        
        time.sleep(3)  # 等待IP轮换
    
    return test_results

def update_local_config():
    """更新本地配置文件"""
    print(f"\n📝 更新本地配置...")
    
    # 更新.env文件
    env_content = f"""# Webshare.io 日本代理配置
# 更新时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
# 限制国家: JP (日本)

# Webshare 代理认证信息
WEBSHARE_USERNAME=gorkpiln
WEBSHARE_PASSWORD=6lwffvf16jjn
WEBSHARE_ENDPOINT=p.webshare.io:80

# 代理配置
PROXY_USERNAME=gorkpiln
PROXY_PASSWORD=6lwffvf16jjn
ENABLE_PROXY=true
PROXY_TIMEOUT=25

# 日本地区限制
PROXY_COUNTRIES=JP

# 爬虫配置 - 针对日本地区优化
MAX_CHANNELS_PER_RUN=60
REQUEST_DELAY_MIN=3
REQUEST_DELAY_MAX=6
CONCURRENT_REQUESTS=3

# 日志级别
LOG_LEVEL=INFO

# 数据库配置
DATABASE_URL=sqlite:///youtube_channels.db
"""
    
    with open(".env", "w", encoding="utf-8") as f:
        f.write(env_content)
    
    print("  ✅ .env 文件已更新")
    
    # 更新proxy_list.txt注释
    updated_content = f"""# Webshare.io 日本代理配置
# 更新时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
# 限制国家: JP (日本)
# 用户名: gorkpiln
# 密码: 6lwffvf16jjn

# Webshare Rotating Residential 代理 (日本地区)
gorkpiln:<EMAIL>:80
"""
    
    with open("proxy_list.txt", "w", encoding="utf-8") as f:
        f.write(updated_content)
    
    print("  ✅ proxy_list.txt 已更新")

def main():
    """主函数"""
    print("🇯🇵 Webshare日本代理配置工具")
    print("=" * 60)
    
    api_key = "ulul4v9sr44nvbd2a0v3yhyzpzs2dfgvkdcsnoyg"
    configurator = WebshareJapanConfig(api_key)
    
    # 1. 获取当前配置
    print("📡 获取当前代理配置...")
    config_result = configurator.get_current_config()
    
    if not config_result["success"]:
        print(f"❌ 获取配置失败: {config_result['error']}")
        return
    
    config_data = config_result["data"]
    countries_data = config_data.get("countries", {})
    
    # 2. 显示日本IP信息
    japan_ips = configurator.show_japan_ip_info(countries_data)
    
    if japan_ips == 0:
        print("❌ 没有日本IP可用")
        return
    
    # 3. 检查当前限制
    current_restrictions = config_data.get("ip_authorization_country_codes")
    print(f"\n🔧 当前国家限制: {current_restrictions or '无限制'}")
    
    # 4. 设置日本代理
    print(f"\n⚙️ 设置日本代理...")
    
    result = configurator.set_japan_proxy()
    
    if result["success"]:
        print("✅ 日本代理设置成功!")
        print(f"   限制国家: JP (日本)")
        print(f"   可用IP数: {japan_ips:,}")
        
        # 5. 更新本地配置
        update_local_config()
        
        # 6. 测试新配置
        print(f"\n🧪 测试日本代理 (需要等待几分钟生效)...")
        print("   注意: 配置更改可能需要5-10分钟生效")
        
        test_results = test_japan_proxy()
        
        # 分析测试结果
        japan_count = sum(1 for r in test_results if r.get("is_japan", False))
        
        print(f"\n📊 测试结果:")
        print(f"  总测试次数: {len(test_results)}")
        print(f"  日本IP次数: {japan_count}")
        print(f"  日本IP比例: {japan_count/len(test_results)*100:.1f}%" if test_results else "  日本IP比例: 0%")
        
        if japan_count > 0:
            print(f"\n🎉 日本代理配置成功!")
            print(f"💡 下一步:")
            print(f"1. 等待5-10分钟让配置完全生效")
            print(f"2. 测试代理: python test_correct_proxy.py")
            print(f"3. 开始爬取: python main.py --mode once")
        else:
            print(f"\n⚠️  配置已设置，但测试中未获得日本IP")
            print(f"💡 建议:")
            print(f"1. 等待更长时间让配置生效")
            print(f"2. 检查VPN是否影响连接")
            print(f"3. 先用无代理模式测试")
        
    else:
        print(f"❌ 设置失败: {result['error']}")
        if "details" in result:
            print(f"   详情: {result['details']}")

if __name__ == '__main__':
    main()
