#!/usr/bin/env python3
"""
深度诊断Webshare代理连接问题
全面检查网络、DNS、防火墙等可能的问题

作者: GreenJoson
创建时间: 2025-06-26
"""

import requests
import socket
import subprocess
import platform
import time
import json

def check_current_ip():
    """检查当前真实IP"""
    print("🌐 检查当前网络状态...")
    
    try:
        # 检查多个IP检测服务
        ip_services = [
            "https://httpbin.org/ip",
            "https://api.ipify.org?format=json",
            "https://ipinfo.io/json"
        ]
        
        for service in ip_services:
            try:
                response = requests.get(service, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    current_ip = data.get('ip') or data.get('origin') or data.get('query')
                    print(f"  ✅ 当前IP: {current_ip} (来源: {service})")
                    
                    # 获取地理位置信息
                    if 'country' in data:
                        print(f"     位置: {data.get('city', '')}, {data.get('country', '')}")
                    
                    return current_ip
            except:
                continue
                
        print("  ❌ 无法获取当前IP")
        return None
        
    except Exception as e:
        print(f"  ❌ 网络检查失败: {e}")
        return None

def check_dns_resolution():
    """详细检查DNS解析"""
    print("\n🔍 DNS解析详细检查...")
    
    hosts = [
        "p.webshare.io",
        "rotating-residential.webshare.io", 
        "proxy.webshare.io",
        "webshare.io"
    ]
    
    for host in hosts:
        try:
            # 获取IP地址
            ip = socket.gethostbyname(host)
            print(f"  ✅ {host} -> {ip}")
            
            # 尝试反向DNS
            try:
                reverse = socket.gethostbyaddr(ip)
                print(f"     反向DNS: {reverse[0]}")
            except:
                pass
                
        except Exception as e:
            print(f"  ❌ {host} -> DNS解析失败: {e}")

def check_port_connectivity():
    """检查端口连通性"""
    print("\n🔌 端口连通性检查...")
    
    test_configs = [
        ("p.webshare.io", 80),
        ("p.webshare.io", 443),
        ("p.webshare.io", 8080),
        ("p.webshare.io", 1080),
        ("rotating-residential.webshare.io", 80),
        ("proxy.webshare.io", 80)
    ]
    
    for host, port in test_configs:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"  ✅ {host}:{port} - 连接成功")
            else:
                print(f"  ❌ {host}:{port} - 连接失败 (错误码: {result})")
                
        except Exception as e:
            print(f"  ❌ {host}:{port} - 异常: {e}")

def check_system_proxy():
    """检查系统代理设置"""
    print("\n⚙️ 系统代理设置检查...")
    
    import os
    
    # 检查环境变量
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY']
    
    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            print(f"  ⚠️  发现系统代理: {var}={value}")
        else:
            print(f"  ✅ {var}: 未设置")

def check_firewall_and_security():
    """检查防火墙和安全软件"""
    print("\n🛡️ 防火墙和安全检查...")
    
    system = platform.system()
    
    if system == "Darwin":  # macOS
        try:
            # 检查macOS防火墙
            result = subprocess.run(['sudo', 'pfctl', '-s', 'info'], 
                                  capture_output=True, text=True, timeout=10)
            if "Status: Enabled" in result.stdout:
                print("  ⚠️  macOS防火墙已启用")
            else:
                print("  ✅ macOS防火墙未启用")
        except:
            print("  ❓ 无法检查macOS防火墙状态")
    
    # 检查常见安全软件进程
    security_processes = ['Little Snitch', 'Lulu', 'Radio Silence', 'Hands Off']
    
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True, timeout=10)
        running_security = []
        
        for process in security_processes:
            if process.lower() in result.stdout.lower():
                running_security.append(process)
        
        if running_security:
            print(f"  ⚠️  发现安全软件: {', '.join(running_security)}")
        else:
            print("  ✅ 未发现常见安全软件")
            
    except:
        print("  ❓ 无法检查安全软件")

def test_direct_webshare_connection():
    """直接测试Webshare服务器连接"""
    print("\n🎯 直接连接Webshare服务器...")
    
    # 测试不同的连接方式
    test_urls = [
        "https://webshare.io",
        "https://proxy.webshare.io/api/v2/profile/",
        "http://p.webshare.io"
    ]
    
    for url in test_urls:
        try:
            response = requests.get(url, timeout=15, headers={
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
            })
            
            print(f"  ✅ {url} - 状态码: {response.status_code}")
            
        except requests.exceptions.Timeout:
            print(f"  ❌ {url} - 连接超时")
        except requests.exceptions.ConnectionError as e:
            print(f"  ❌ {url} - 连接错误: {str(e)[:100]}...")
        except Exception as e:
            print(f"  ❌ {url} - 其他错误: {str(e)[:100]}...")

def test_alternative_proxy_methods():
    """测试替代的代理连接方法"""
    print("\n🔄 测试替代连接方法...")
    
    username = "gorkpiln"
    password = "6lwffvf16jjn"
    
    # 方法1: 使用requests.Session
    print("  方法1: 使用Session...")
    try:
        session = requests.Session()
        session.proxies = {
            'http': f'http://{username}:{password}@p.webshare.io:80',
            'https': f'http://{username}:{password}@p.webshare.io:80'
        }
        
        response = session.get('http://httpbin.org/ip', timeout=15)
        if response.status_code == 200:
            print(f"    ✅ Session方法成功: {response.json().get('origin')}")
        else:
            print(f"    ❌ Session方法失败: {response.status_code}")
            
    except Exception as e:
        print(f"    ❌ Session方法异常: {str(e)[:100]}...")
    
    # 方法2: 使用urllib
    print("  方法2: 使用urllib...")
    try:
        import urllib.request
        
        proxy_handler = urllib.request.ProxyHandler({
            'http': f'http://{username}:{password}@p.webshare.io:80',
            'https': f'http://{username}:{password}@p.webshare.io:80'
        })
        
        opener = urllib.request.build_opener(proxy_handler)
        urllib.request.install_opener(opener)
        
        response = urllib.request.urlopen('http://httpbin.org/ip', timeout=15)
        data = json.loads(response.read().decode())
        print(f"    ✅ urllib方法成功: {data.get('origin')}")
        
    except Exception as e:
        print(f"    ❌ urllib方法异常: {str(e)[:100]}...")

def check_network_route():
    """检查网络路由"""
    print("\n🛣️ 网络路由检查...")
    
    try:
        # 检查到Webshare服务器的路由
        result = subprocess.run(['traceroute', '-m', '10', 'p.webshare.io'], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')[:5]  # 只显示前5跳
            print("  路由跟踪 (前5跳):")
            for line in lines:
                print(f"    {line}")
        else:
            print("  ❌ 路由跟踪失败")
            
    except subprocess.TimeoutExpired:
        print("  ⚠️  路由跟踪超时")
    except Exception as e:
        print(f"  ❌ 路由跟踪异常: {e}")

def main():
    """主函数"""
    print("🔧 Webshare代理深度诊断工具")
    print("=" * 60)
    
    # 1. 检查当前网络状态
    current_ip = check_current_ip()
    
    # 2. DNS解析检查
    check_dns_resolution()
    
    # 3. 端口连通性检查
    check_port_connectivity()
    
    # 4. 系统代理检查
    check_system_proxy()
    
    # 5. 防火墙和安全检查
    check_firewall_and_security()
    
    # 6. 直接连接测试
    test_direct_webshare_connection()
    
    # 7. 替代方法测试
    test_alternative_proxy_methods()
    
    # 8. 网络路由检查
    check_network_route()
    
    print("\n" + "=" * 60)
    print("📊 诊断总结:")
    print("1. 如果DNS解析失败 -> 可能是DNS服务器问题")
    print("2. 如果端口连接失败 -> 可能是防火墙或ISP限制")
    print("3. 如果发现系统代理 -> 可能与Webshare代理冲突")
    print("4. 如果安全软件运行 -> 可能阻止代理连接")
    print("5. 如果直接连接失败 -> 可能是网络或ISP问题")
    
    print(f"\n💡 基于诊断结果的建议:")
    print("- 尝试不同网络环境 (手机热点)")
    print("- 检查路由器设置")
    print("- 联系ISP确认是否有代理限制")
    print("- 暂时使用无代理模式开发")

if __name__ == '__main__':
    main()
