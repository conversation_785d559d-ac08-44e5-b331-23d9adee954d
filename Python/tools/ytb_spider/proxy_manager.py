"""
代理管理模块
负责代理IP的管理、测试和轮换，提供防对抗功能
"""

import random
import time
import requests
from typing import List, Dict, Optional, Tuple
from pathlib import Path
from loguru import logger
from config import PROXY_CONFIG, USER_AGENTS


class ProxyManager:
    """代理管理器类，处理代理IP的管理和轮换"""

    def __init__(self):
        """初始化代理管理器"""
        self.proxies: List[Dict] = []
        self.current_proxy_index = 0
        self.failed_proxies = set()
        self.proxy_failures = {}
        self.load_proxies()
        logger.info(f"代理管理器初始化完成，加载了 {len(self.proxies)} 个代理")

    def load_proxies(self):
        """从文件加载代理列表"""
        proxy_file = PROXY_CONFIG['proxy_list_file']

        if not proxy_file.exists():
            logger.warning(f"代理文件不存在: {proxy_file}")
            self.create_sample_proxy_file(proxy_file)
            return

        try:
            with open(proxy_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            for line in lines:
                line = line.strip()
                if line and not line.startswith('#'):
                    proxy_dict = self.parse_proxy_line(line)
                    if proxy_dict:
                        self.proxies.append(proxy_dict)

            logger.info(f"成功加载 {len(self.proxies)} 个代理")

        except Exception as e:
            logger.error(f"加载代理文件失败: {e}")

    def parse_proxy_line(self, line: str) -> Optional[Dict]:
        """
        解析代理配置行

        支持格式:
        - ip:port
        - username:password@ip:port
        - http://ip:port
        - ***************************:port

        Args:
            line: 代理配置行

        Returns:
            Dict: 代理配置字典
        """
        try:
            line = line.strip()

            # 处理带协议的格式
            if line.startswith(('http://', 'https://', 'socks4://', 'socks5://')):
                return {'http': line, 'https': line}

            # 处理带认证的格式
            if '@' in line:
                auth_part, addr_part = line.split('@', 1)
                username, password = auth_part.split(':', 1)
                ip, port = addr_part.split(':', 1)

                proxy_url = f"http://{username}:{password}@{ip}:{port}"
                return {'http': proxy_url, 'https': proxy_url}

            # 处理简单的ip:port格式
            if ':' in line:
                ip, port = line.split(':', 1)
                proxy_url = f"http://{ip}:{port}"
                return {'http': proxy_url, 'https': proxy_url}

            return None

        except Exception as e:
            logger.warning(f"解析代理配置失败: {line}, 错误: {e}")
            return None

    def create_sample_proxy_file(self, proxy_file: Path):
        """创建示例代理文件"""
        sample_content = """# 代理配置文件
# 支持以下格式:
# ip:port
# username:password@ip:port
# http://ip:port
# ***************************:port

# 示例代理 (请替换为真实的代理)
# 127.0.0.1:8080
# user:<EMAIL>:8080
# http://proxy1.example.com:3128
# socks5://proxy2.example.com:1080

# 免费代理示例 (不保证可用性)
# ***********:80
# ************:8888
# *************:3128
"""

        try:
            proxy_file.parent.mkdir(exist_ok=True)
            with open(proxy_file, 'w', encoding='utf-8') as f:
                f.write(sample_content)
            logger.info(f"已创建示例代理文件: {proxy_file}")
        except Exception as e:
            logger.error(f"创建代理文件失败: {e}")

    def get_random_proxy(self) -> Optional[Dict]:
        """
        获取随机代理

        Returns:
            Dict: 代理配置字典，如果没有可用代理返回None
        """
        if not PROXY_CONFIG['enabled'] or not self.proxies:
            return None

        available_proxies = [p for p in self.proxies if p not in self.failed_proxies]

        if not available_proxies:
            logger.warning("没有可用的代理，重置失败代理列表")
            self.failed_proxies.clear()
            self.proxy_failures.clear()
            available_proxies = self.proxies

        if available_proxies:
            return random.choice(available_proxies)

        return None

    def get_next_proxy(self) -> Optional[Dict]:
        """
        获取下一个代理（轮换方式）

        Returns:
            Dict: 代理配置字典
        """
        if not PROXY_CONFIG['enabled'] or not self.proxies:
            return None

        available_proxies = [p for p in self.proxies if p not in self.failed_proxies]

        if not available_proxies:
            self.failed_proxies.clear()
            self.proxy_failures.clear()
            available_proxies = self.proxies

        if available_proxies:
            proxy = available_proxies[self.current_proxy_index % len(available_proxies)]
            self.current_proxy_index += 1
            return proxy

        return None

    def test_proxy(self, proxy: Dict) -> Tuple[bool, float]:
        """
        测试代理是否可用

        Args:
            proxy: 代理配置字典

        Returns:
            Tuple[bool, float]: (是否可用, 响应时间)
        """
        try:
            start_time = time.time()

            response = requests.get(
                PROXY_CONFIG['proxy_test_url'],
                proxies=proxy,
                timeout=PROXY_CONFIG['proxy_timeout'],
                headers={'User-Agent': random.choice(USER_AGENTS)}
            )

            response_time = time.time() - start_time

            if response.status_code == 200:
                logger.debug(f"代理测试成功: {proxy}, 响应时间: {response_time:.2f}s")
                return True, response_time
            else:
                logger.warning(f"代理测试失败: {proxy}, 状态码: {response.status_code}")
                return False, response_time

        except Exception as e:
            logger.warning(f"代理测试异常: {proxy}, 错误: {e}")
            return False, 0.0

    def mark_proxy_failed(self, proxy: Dict):
        """
        标记代理为失败状态

        Args:
            proxy: 代理配置字典
        """
        proxy_key = str(proxy)
        self.proxy_failures[proxy_key] = self.proxy_failures.get(proxy_key, 0) + 1

        if self.proxy_failures[proxy_key] >= PROXY_CONFIG['max_proxy_failures']:
            self.failed_proxies.add(proxy)
            logger.warning(f"代理已标记为失败: {proxy}")

    def get_random_user_agent(self) -> str:
        """
        获取随机User-Agent

        Returns:
            str: 随机User-Agent字符串
        """
        return random.choice(USER_AGENTS)

    def get_request_headers(self) -> Dict[str, str]:
        """
        获取请求头

        Returns:
            Dict: 请求头字典
        """
        return {
            'User-Agent': self.get_random_user_agent(),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }

    def make_request(self, url: str, **kwargs) -> Optional[requests.Response]:
        """
        使用代理发起请求

        Args:
            url: 请求URL
            **kwargs: requests.get的其他参数

        Returns:
            requests.Response: 响应对象，失败返回None
        """
        max_retries = PROXY_CONFIG.get('max_proxy_failures', 3)

        for attempt in range(max_retries):
            proxy = self.get_random_proxy()
            headers = self.get_request_headers()

            try:
                response = requests.get(
                    url,
                    proxies=proxy,
                    headers=headers,
                    timeout=PROXY_CONFIG['proxy_timeout'],
                    **kwargs
                )

                if response.status_code == 200:
                    return response
                else:
                    logger.warning(f"请求失败，状态码: {response.status_code}")
                    if proxy:
                        self.mark_proxy_failed(proxy)

            except Exception as e:
                logger.warning(f"请求异常: {e}")
                if proxy:
                    self.mark_proxy_failed(proxy)

            # 请求间隔
            time.sleep(random.uniform(1, 3))

        logger.error(f"请求最终失败: {url}")
        return None

    def get_proxy_stats(self) -> Dict:
        """
        获取代理统计信息

        Returns:
            Dict: 代理统计信息
        """
        return {
            'total_proxies': len(self.proxies),
            'failed_proxies': len(self.failed_proxies),
            'available_proxies': len(self.proxies) - len(self.failed_proxies),
            'proxy_failures': dict(self.proxy_failures),
        }


if __name__ == '__main__':
    # 测试代理管理器功能
    proxy_manager = ProxyManager()
    stats = proxy_manager.get_proxy_stats()
    print(f"代理统计信息: {stats}")

    # 测试代理连接
    if proxy_manager.proxies:
        proxy = proxy_manager.get_random_proxy()
        if proxy:
            is_working, response_time = proxy_manager.test_proxy(proxy)
            print(f"代理测试结果: 可用={is_working}, 响应时间={response_time:.2f}秒")
