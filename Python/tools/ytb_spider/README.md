# YouTube Twitter爬虫

一个自动发现YouTube频道并提取其中Twitter/X链接的爬虫工具。

## 功能特点

- 🔍 **自动频道发现**: 通过关键词搜索自动发现新的YouTube频道
- 🐦 **Twitter链接提取**: 智能识别频道中的Twitter/X链接
- 🛡️ **防对抗机制**: 支持代理IP轮换、请求间隔控制、User-Agent轮换
- 💾 **数据持久化**: 使用SQLite数据库存储频道信息和爬取状态
- 🔄 **无限运行**: 支持持续运行模式，自动发现和爬取新频道
- 📊 **统计报告**: 提供详细的爬取统计信息
- 🧪 **完整测试**: 包含单元测试和集成测试

## 项目结构

```
ytb_spider/
├── main.py              # 主程序入口
├── spider.py            # 爬虫核心逻辑
├── database.py          # 数据库操作
├── proxy_manager.py     # 代理管理
├── utils.py             # 工具函数
├── config.py            # 配置文件
├── requirements.txt     # 依赖包
├── test_v1.py          # 测试文件
├── .env.example        # 环境配置示例
├── README.md           # 项目说明
├── changelog.md        # 更新日志
├── proxy_list.txt      # 代理列表（需自行创建）
├── logs/               # 日志目录
├── data/               # 数据目录
└── backups/            # 备份目录
```

## 安装和配置

### 1. 环境要求

- Python 3.8+
- 虚拟环境（推荐）

### 2. 安装依赖

```bash
# 激活虚拟环境
source /Users/<USER>/Projects/Python/venv/bin/activate

# 安装依赖包
pip install -r requirements.txt
```

### 3. 配置文件

复制环境配置文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，填入相关配置（可选）。

### 4. 代理配置（可选但推荐）

#### 方式1: 使用 Webshare.io（推荐）

Webshare.io 是高质量的代理服务提供商，我们提供了专门的配置工具：

```bash
# 交互式配置 Webshare 代理
python webshare_setup.py -i

# 测试现有代理
python webshare_setup.py -t

# 从文件转换代理格式
python webshare_setup.py -c webshare_proxies.txt -u 用户名 -p 密码
```

#### 方式2: 手动配置

创建 `proxy_list.txt` 文件，添加代理服务器：

```
# 代理配置格式示例
127.0.0.1:8080
username:<EMAIL>:3128
http://proxy1.example.com:8080
socks5://proxy2.example.com:1080

# Webshare.io 格式示例
webshare_user:webshare_pass@************:8080
webshare_user:webshare_pass@************:8080
```

## 使用方法

### 运行测试

```bash
# 运行测试确保所有功能正常
python test_v1.py
```

### 单次运行

```bash
# 运行一次完整的发现和爬取周期
python main.py --mode once
```

### 持续运行

```bash
# 持续运行模式（推荐）
python main.py --mode continuous

# 自定义间隔时间
python main.py --mode continuous --discovery-interval 7200 --crawl-interval 600
```

### 参数说明

- `--mode`: 运行模式
  - `once`: 单次运行
  - `continuous`: 持续运行（默认）
- `--discovery-interval`: 频道发现间隔（秒），默认3600秒
- `--crawl-interval`: 频道爬取间隔（秒），默认300秒

## 数据库结构

### channels表
存储频道基本信息：
- `channel_id`: 频道ID
- `channel_url`: 频道URL
- `channel_name`: 频道名称
- `subscriber_count`: 订阅者数量
- `video_count`: 视频数量
- `description`: 频道描述
- `twitter_links`: Twitter链接（JSON格式）
- `crawl_status`: 爬取状态
- `created_at`: 创建时间
- `updated_at`: 更新时间

### crawl_logs表
存储爬取日志：
- `channel_id`: 频道ID
- `action`: 操作类型
- `status`: 操作状态
- `message`: 详细信息
- `created_at`: 创建时间

## 配置说明

### 爬虫配置
- `request_delay`: 请求间隔范围（秒）
- `max_retries`: 最大重试次数
- `timeout`: 请求超时时间
- `max_channels_per_run`: 每次运行最大爬取频道数

### 代理配置
- `enabled`: 是否启用代理
- `proxy_timeout`: 代理测试超时时间
- `max_proxy_failures`: 代理最大失败次数

### Twitter识别配置
- 支持 `twitter.com` 和 `x.com` 域名
- 支持 `@username` 格式
- 自动标准化为 `https://x.com/username` 格式

## 日志和监控

### 日志文件
- 位置: `logs/spider.log`
- 自动轮换: 10MB
- 保留时间: 7天

### 统计信息
程序会定期输出统计信息：
- 总爬取频道数
- 发现Twitter频道数
- Twitter发现率
- 代理使用情况

## 注意事项

1. **遵守robots.txt**: 请遵守YouTube的robots.txt规则
2. **合理的请求频率**: 默认配置已设置合理的请求间隔
3. **代理使用**: 建议使用代理IP以避免被封禁
4. **数据备份**: 定期备份SQLite数据库文件
5. **法律合规**: 仅用于学习和研究目的，请遵守相关法律法规

## 故障排除

### 常见问题

1. **代理连接失败**
   - 检查代理配置格式
   - 测试代理是否可用
   - 查看日志文件获取详细错误信息

2. **频道发现失败**
   - 检查网络连接
   - 尝试更换搜索关键词
   - 检查是否被YouTube限制

3. **数据库错误**
   - 检查数据库文件权限
   - 确保磁盘空间充足
   - 查看错误日志

### 调试模式

修改 `config.py` 中的日志级别为 `DEBUG` 获取更详细的日志信息。

## 开发和贡献

### 代码规范
- 所有函数都有详细注释
- 遵循PEP 8代码风格
- 每次修改都要更新changelog.md

### 测试
- 运行 `python test_v1.py` 进行测试
- 新功能需要添加相应的测试用例

## 更新日志

详见 [changelog.md](changelog.md)

## 许可证

本项目仅供学习和研究使用。

## 作者

GreenJoson - 2025-06-26
