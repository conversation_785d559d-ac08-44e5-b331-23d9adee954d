#!/usr/bin/env python3
"""
切换回英文地区代理
使用美国、英国、加拿大、澳大利亚的IP

作者: GreenJoson
创建时间: 2025-06-26
"""

import requests
import json
import time

class WebshareEnglishSwitch:
    """切换到英文地区代理"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://proxy.webshare.io/api/v2"
        self.headers = {
            "Authorization": f"Token {api_key}",
            "Content-Type": "application/json"
        }
        
        # 英语国家代码
        self.english_countries = ["US", "GB", "CA", "AU"]
    
    def switch_to_english_regions(self):
        """切换到英文地区"""
        try:
            update_data = {
                "ip_authorization_country_codes": self.english_countries
            }
            
            response = requests.patch(
                f"{self.base_url}/proxy/config/",
                headers=self.headers,
                json=update_data,
                timeout=15
            )
            
            if response.status_code == 200:
                return {"success": True, "data": response.json()}
            else:
                return {"success": False, "error": f"HTTP {response.status_code}", "details": response.text}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_english_ip_count(self):
        """获取英文地区IP数量"""
        try:
            response = requests.get(
                f"{self.base_url}/proxy/config/",
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                config_data = response.json()
                countries_data = config_data.get("countries", {})
                
                english_total = sum(countries_data.get(code, 0) for code in self.english_countries)
                
                print("🇺🇸🇬🇧🇨🇦🇦🇺 英文地区IP分布:")
                print("=" * 50)
                
                country_names = {
                    "US": "美国 (United States)",
                    "GB": "英国 (United Kingdom)", 
                    "CA": "加拿大 (Canada)",
                    "AU": "澳大利亚 (Australia)"
                }
                
                for code in self.english_countries:
                    count = countries_data.get(code, 0)
                    name = country_names.get(code, code)
                    percentage = (count / english_total * 100) if english_total > 0 else 0
                    print(f"  {name}: {count:,} IPs ({percentage:.1f}%)")
                
                print(f"\n📊 英文地区总计: {english_total:,} IPs")
                return english_total
            else:
                print(f"❌ 获取配置失败: HTTP {response.status_code}")
                return 0
                
        except Exception as e:
            print(f"❌ 获取配置异常: {e}")
            return 0

def test_english_proxy():
    """测试英文地区代理"""
    print(f"\n🧪 测试英文地区代理...")
    
    # 使用基础用户名（不带地区后缀）
    username = "gorkpiln"
    password = "6lwffvf16jjn"
    
    # 测试不同的代理配置
    proxy_configs = [
        f"http://{username}:{password}@p.webshare.io:80",
        f"http://{username}:{password}@rotating-residential.webshare.io:80",
        f"http://{username}:{password}@proxy.webshare.io:80"
    ]
    
    working_proxies = []
    
    for i, proxy_url in enumerate(proxy_configs, 1):
        print(f"\n[{i}/{len(proxy_configs)}] 测试: {proxy_url}")
        
        proxies = {
            "http": proxy_url,
            "https": proxy_url
        }
        
        try:
            start_time = time.time()
            response = requests.get(
                "http://ip-api.com/json/",
                proxies=proxies,
                timeout=20,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
                }
            )
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                country_code = data.get('countryCode', '')
                
                print(f"  ✅ 连接成功!")
                print(f"    代理IP: {data.get('query')}")
                print(f"    位置: {data.get('city')}, {data.get('country')}")
                print(f"    国家代码: {country_code}")
                print(f"    响应时间: {response_time:.2f}秒")
                
                if country_code in ["US", "GB", "CA", "AU"]:
                    print(f"    🎯 确认为英文地区IP!")
                    working_proxies.append({
                        "url": proxy_url,
                        "ip": data.get('query'),
                        "country": data.get('country'),
                        "country_code": country_code
                    })
                else:
                    print(f"    ⚠️  非英文地区IP: {data.get('country')}")
                    
            else:
                print(f"  ❌ 连接失败 - 状态码: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ 连接异常: {str(e)[:100]}...")
        
        time.sleep(3)
    
    return working_proxies

def update_proxy_config(working_proxies):
    """更新代理配置文件"""
    print(f"\n📝 更新代理配置...")
    
    # 更新proxy_list.txt
    proxy_content = f"""# Webshare.io 英文地区代理配置
# 更新时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
# 限制国家: US, GB, CA, AU (英文地区)
# 用户名: gorkpiln (基础用户名，无地区后缀)
# 密码: 6lwffvf16jjn

# Webshare Rotating Residential 代理 (英文地区)
"""
    
    if working_proxies:
        proxy_content += "# 已验证可用的代理:\n"
        for proxy in working_proxies:
            auth_part = proxy['url'].replace('http://', '').replace('https://', '')
            proxy_content += f"{auth_part}  # {proxy['country']} ({proxy['country_code']})\n"
    else:
        proxy_content += "# 基础配置 (待验证):\n"
        proxy_content += "gorkpiln:<EMAIL>:80\n"
    
    with open("proxy_list.txt", "w", encoding="utf-8") as f:
        f.write(proxy_content)
    
    print("  ✅ proxy_list.txt 已更新")
    
    # 更新.env文件
    env_content = f"""# Webshare.io 英文地区代理配置
# 更新时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
# 限制国家: US, GB, CA, AU

# Webshare 代理认证信息
WEBSHARE_USERNAME=gorkpiln
WEBSHARE_PASSWORD=6lwffvf16jjn
WEBSHARE_ENDPOINT=p.webshare.io:80

# 代理配置
PROXY_USERNAME=gorkpiln
PROXY_PASSWORD=6lwffvf16jjn
ENABLE_PROXY=true
PROXY_TIMEOUT=25

# 英文地区限制
PROXY_COUNTRIES=US,GB,CA,AU

# 爬虫配置 - 针对英文地区优化
MAX_CHANNELS_PER_RUN=60
REQUEST_DELAY_MIN=3
REQUEST_DELAY_MAX=6
CONCURRENT_REQUESTS=3

# 日志级别
LOG_LEVEL=INFO

# 数据库配置
DATABASE_URL=sqlite:///youtube_channels.db
"""
    
    with open(".env", "w", encoding="utf-8") as f:
        f.write(env_content)
    
    print("  ✅ .env 文件已更新")

def main():
    """主函数"""
    print("🇺🇸 切换到英文地区代理")
    print("使用美国、英国、加拿大、澳大利亚IP")
    print("=" * 60)
    
    api_key = "ulul4v9sr44nvbd2a0v3yhyzpzs2dfgvkdcsnoyg"
    switcher = WebshareEnglishSwitch(api_key)
    
    # 1. 显示英文地区IP信息
    english_ip_count = switcher.get_english_ip_count()
    
    if english_ip_count == 0:
        print("❌ 无法获取IP信息")
        return
    
    # 2. 切换到英文地区
    print(f"\n⚙️ 切换到英文地区...")
    result = switcher.switch_to_english_regions()
    
    if result["success"]:
        print("✅ 英文地区代理设置成功!")
        print(f"   限制国家: {', '.join(switcher.english_countries)}")
        print(f"   可用IP数: {english_ip_count:,}")
        
        # 3. 更新本地配置
        update_proxy_config([])
        
        # 4. 等待配置生效后测试
        print(f"\n⏳ 等待配置生效 (5分钟)...")
        print("   配置更改需要时间同步到代理服务器")
        
        # 可以选择等待或立即测试
        print(f"\n💡 选择:")
        print(f"1. 立即测试 (可能还未生效)")
        print(f"2. 等待5分钟后测试")
        print(f"3. 先用无代理模式开发")
        
        # 立即测试一次
        working_proxies = test_english_proxy()
        
        if working_proxies:
            print(f"\n🎉 英文地区代理配置成功!")
            print(f"✅ 找到 {len(working_proxies)} 个工作代理")
            
            # 更新配置文件包含工作的代理
            update_proxy_config(working_proxies)
            
            print(f"\n📝 下一步:")
            print(f"1. 运行爬虫: python main.py --mode once")
            print(f"2. 测试Twitter映射: python test_twitter_mapping.py")
            
        else:
            print(f"\n⚠️  代理配置已设置，但测试连接失败")
            print(f"💡 建议:")
            print(f"1. 等待10分钟后重新测试")
            print(f"2. 检查网络环境")
            print(f"3. 先用无代理模式: python test_no_proxy.py")
        
    else:
        print(f"❌ 设置失败: {result['error']}")
        if "details" in result:
            print(f"   详情: {result['details']}")

if __name__ == '__main__':
    main()
