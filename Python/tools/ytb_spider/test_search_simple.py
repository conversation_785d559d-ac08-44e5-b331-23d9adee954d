#!/usr/bin/env python3
"""
简单测试YouTube搜索功能
不使用代理，直接测试搜索

作者: GreenJoson
创建时间: 2025-06-26
"""

import sys
import requests
import re
from pathlib import Path
from bs4 import BeautifulSoup

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

def extract_twitter_links(html_content):
    """提取HTML中的Twitter链接"""
    twitter_links = set()

    # Twitter/X链接的正则表达式
    twitter_patterns = [
        r'https?://(?:www\.)?twitter\.com/[a-zA-Z0-9_]+',
        r'https?://(?:www\.)?x\.com/[a-zA-Z0-9_]+',
        r'https?://twitter\.com/[a-zA-Z0-9_]+',
        r'https?://x\.com/[a-zA-Z0-9_]+',
    ]

    for pattern in twitter_patterns:
        matches = re.findall(pattern, html_content, re.IGNORECASE)
        twitter_links.update(matches)

    # 清理链接，移除查询参数
    cleaned_links = []
    for link in twitter_links:
        # 移除查询参数和片段
        clean_link = link.split('?')[0].split('#')[0]
        if clean_link not in cleaned_links:
            cleaned_links.append(clean_link)

    return sorted(cleaned_links)

def test_youtube_search():
    """测试YouTube搜索"""
    print("🔍 测试YouTube搜索功能")
    print("=" * 50)

    # 简单的搜索URL
    keyword = "gaming"
    search_url = f"https://www.youtube.com/results?search_query={keyword}&sp=EgIQAg%253D%253D"

    print(f"搜索关键词: {keyword}")
    print(f"搜索URL: {search_url}")

    try:
        # 发送请求
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }

        response = requests.get(search_url, headers=headers, timeout=15)
        print(f"响应状态码: {response.status_code}")

        if response.status_code == 200:
            print(f"响应内容长度: {len(response.text)}")

            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')

            # 查找频道链接
            channel_links = []
            for link in soup.find_all('a', href=True):
                href = link['href']
                if '/channel/' in href or '/@' in href or '/c/' in href or '/user/' in href:
                    if href.startswith('/'):
                        full_url = f"https://www.youtube.com{href}"
                        channel_links.append(full_url)

            print(f"找到 {len(channel_links)} 个频道链接:")
            for i, link in enumerate(channel_links[:10], 1):  # 只显示前10个
                print(f"  {i}. {link}")

            if channel_links:
                print("✅ 搜索功能正常")
                return True
            else:
                print("⚠️  没有找到频道链接，可能需要调整解析逻辑")

                # 保存HTML用于调试
                with open("debug_search.html", "w", encoding="utf-8") as f:
                    f.write(response.text)
                print("已保存HTML到 debug_search.html 用于调试")
                return False
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ 搜索异常: {e}")
        return False

def test_channel_access():
    """测试频道访问"""
    print("\n🎯 测试频道访问")
    print("=" * 50)

    # 测试一个知名频道
    test_channels = [
        "https://www.youtube.com/@MrBeast",
        "https://www.youtube.com/channel/UCX6OQ3DkcsbYNE6H8uQQuVA",  # MrBeast
    ]

    for channel_url in test_channels:
        try:
            print(f"\n测试频道: {channel_url}")

            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }

            response = requests.get(channel_url, headers=headers, timeout=15)
            print(f"  状态码: {response.status_code}")

            if response.status_code == 200:
                print(f"  内容长度: {len(response.text)}")

                # 提取Twitter链接
                twitter_links = extract_twitter_links(response.text)

                if twitter_links:
                    print(f"  ✅ 发现 {len(twitter_links)} 个Twitter链接:")
                    for i, link in enumerate(twitter_links, 1):
                        print(f"    {i}. {link}")
                else:
                    print("  ⚠️  未发现Twitter链接")

                print("  ✅ 频道访问成功")
            else:
                print(f"  ❌ 访问失败: {response.status_code}")

        except Exception as e:
            print(f"  ❌ 访问异常: {e}")

def main():
    """主函数"""
    print("🧪 YouTube搜索简单测试")
    print("测试无代理模式下的基本功能")
    print()

    # 测试搜索
    search_ok = test_youtube_search()

    # 测试频道访问
    test_channel_access()

    print("\n" + "=" * 50)
    print("📊 测试总结:")

    if search_ok:
        print("✅ 基本搜索功能正常")
        print("💡 可以继续调试爬虫的其他问题")
    else:
        print("❌ 搜索功能有问题")
        print("💡 可能需要:")
        print("   1. 调整User-Agent")
        print("   2. 添加更多请求头")
        print("   3. 处理JavaScript渲染")
        print("   4. 使用Selenium")

if __name__ == '__main__':
    main()
