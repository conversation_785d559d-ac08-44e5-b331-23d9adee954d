#!/usr/bin/env python3
"""
测试YouTube-Twitter映射功能
展示频道和Twitter账号的对应关系

作者: GreenJoson
创建时间: 2025-06-26
"""

import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from database import DatabaseManager
from spider import YouTubeSpider
from loguru import logger

def test_twitter_mapping():
    """测试Twitter映射功能"""
    print("🐦 YouTube-Twitter映射测试")
    print("=" * 60)
    
    # 知名YouTube频道列表（这些频道通常都有Twitter）
    test_channels = [
        "https://www.youtube.com/@MrBeast",
        "https://www.youtube.com/@PewDiePie", 
        "https://www.youtube.com/@Markiplier",
        "https://www.youtube.com/@LinusTechTips",
        "https://www.youtube.com/@MKBHD",
    ]
    
    # 初始化组件
    db = DatabaseManager()
    spider = YouTubeSpider()
    
    print(f"准备测试 {len(test_channels)} 个频道的Twitter映射")
    
    # 爬取频道并创建映射
    successful_mappings = 0
    
    for i, channel_url in enumerate(test_channels, 1):
        try:
            print(f"\n[{i}/{len(test_channels)}] 🎯 爬取频道: {channel_url}")
            
            # 爬取频道信息
            channel_info = spider.crawl_channel(channel_url)
            
            if channel_info:
                channel_name = channel_info.get('channel_name', 'Unknown')
                twitter_links = channel_info.get('twitter_links', [])
                
                print(f"  ✅ 频道: {channel_name}")
                
                if twitter_links:
                    print(f"  🐦 发现 {len(twitter_links)} 个Twitter链接:")
                    for j, link in enumerate(twitter_links, 1):
                        print(f"     {j}. {link}")
                    successful_mappings += 1
                else:
                    print(f"  ⚠️  未发现Twitter链接")
                    
            else:
                print(f"  ❌ 爬取失败")
                
        except Exception as e:
            print(f"  ❌ 爬取异常: {e}")
    
    print(f"\n" + "=" * 60)
    print(f"📊 爬取结果:")
    print(f"  总测试频道: {len(test_channels)}")
    print(f"  成功映射: {successful_mappings}")
    print(f"  映射成功率: {successful_mappings/len(test_channels)*100:.1f}%")
    
    return successful_mappings > 0

def display_all_mappings():
    """显示所有Twitter映射"""
    print(f"\n🗂️ 所有YouTube-Twitter映射关系")
    print("=" * 60)
    
    db = DatabaseManager()
    mappings = db.get_all_twitter_mappings(limit=50)
    
    if not mappings:
        print("❌ 暂无Twitter映射数据")
        return
    
    print(f"找到 {len(mappings)} 个映射关系:\n")
    
    for i, mapping in enumerate(mappings, 1):
        channel_name = mapping.get('channel_name', 'Unknown')
        youtube_url = mapping.get('youtube_url', '')
        twitter_url = mapping.get('twitter_url', '')
        twitter_username = mapping.get('twitter_username', '')
        discovery_date = mapping.get('discovery_date', '')
        subscriber_count = mapping.get('subscriber_count', 0)
        
        print(f"[{i}] 📺 {channel_name}")
        print(f"    YouTube: {youtube_url}")
        print(f"    Twitter: {twitter_url} ({twitter_username})")
        if subscriber_count:
            print(f"    订阅者: {subscriber_count:,}")
        print(f"    发现时间: {discovery_date}")
        print()

def search_mappings_demo():
    """演示搜索映射功能"""
    print(f"\n🔍 搜索映射演示")
    print("=" * 60)
    
    db = DatabaseManager()
    
    # 测试搜索关键词
    search_terms = ["MrBeast", "Linus", "MKBHD", "tech"]
    
    for term in search_terms:
        print(f"\n搜索关键词: '{term}'")
        results = db.search_twitter_mappings(term)
        
        if results:
            print(f"  找到 {len(results)} 个匹配结果:")
            for result in results[:3]:  # 只显示前3个
                channel_name = result.get('channel_name', 'Unknown')
                twitter_username = result.get('twitter_username', '')
                print(f"    📺 {channel_name} -> 🐦 {twitter_username}")
        else:
            print(f"  ❌ 未找到匹配结果")

def export_mappings():
    """导出映射关系到文件"""
    print(f"\n📤 导出映射关系")
    print("=" * 60)
    
    db = DatabaseManager()
    mappings = db.get_all_twitter_mappings(limit=1000)
    
    if not mappings:
        print("❌ 暂无数据可导出")
        return
    
    # 导出为CSV格式
    csv_content = "YouTube频道名,YouTube链接,Twitter链接,Twitter用户名,订阅者数,发现时间\n"
    
    for mapping in mappings:
        channel_name = mapping.get('channel_name', '').replace(',', '，')  # 替换逗号避免CSV格式问题
        youtube_url = mapping.get('youtube_url', '')
        twitter_url = mapping.get('twitter_url', '')
        twitter_username = mapping.get('twitter_username', '')
        subscriber_count = mapping.get('subscriber_count', 0)
        discovery_date = mapping.get('discovery_date', '')
        
        csv_content += f'"{channel_name}","{youtube_url}","{twitter_url}","{twitter_username}",{subscriber_count},"{discovery_date}"\n'
    
    # 保存到文件
    output_file = "youtube_twitter_mappings.csv"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(csv_content)
    
    print(f"✅ 已导出 {len(mappings)} 个映射关系到: {output_file}")
    
    # 也导出为简单的文本格式
    txt_content = "YouTube-Twitter映射关系\n"
    txt_content += "=" * 50 + "\n\n"
    
    for i, mapping in enumerate(mappings, 1):
        channel_name = mapping.get('channel_name', 'Unknown')
        youtube_url = mapping.get('youtube_url', '')
        twitter_url = mapping.get('twitter_url', '')
        twitter_username = mapping.get('twitter_username', '')
        
        txt_content += f"[{i}] {channel_name}\n"
        txt_content += f"    📺 {youtube_url}\n"
        txt_content += f"    🐦 {twitter_url} ({twitter_username})\n\n"
    
    txt_file = "youtube_twitter_mappings.txt"
    with open(txt_file, 'w', encoding='utf-8') as f:
        f.write(txt_content)
    
    print(f"✅ 已导出文本格式到: {txt_file}")

def main():
    """主函数"""
    print("🚀 YouTube-Twitter映射系统测试")
    print("自动发现并映射YouTube频道与Twitter账号")
    print()
    
    # 1. 测试映射创建
    success = test_twitter_mapping()
    
    if success:
        # 2. 显示所有映射
        display_all_mappings()
        
        # 3. 演示搜索功能
        search_mappings_demo()
        
        # 4. 导出映射关系
        export_mappings()
        
        print(f"\n🎉 Twitter映射系统测试完成!")
        print(f"💡 功能特点:")
        print(f"  ✅ 自动发现YouTube频道中的Twitter链接")
        print(f"  ✅ 建立频道与Twitter账号的映射关系")
        print(f"  ✅ 支持搜索和查询映射关系")
        print(f"  ✅ 支持导出映射数据")
        print(f"  ✅ 自动去重，避免重复映射")
        
    else:
        print(f"\n❌ 映射测试失败")
        print(f"💡 可能的原因:")
        print(f"  1. 网络连接问题")
        print(f"  2. YouTube页面结构变化")
        print(f"  3. 代理配置问题")

if __name__ == '__main__':
    main()
