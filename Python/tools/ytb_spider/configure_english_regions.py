#!/usr/bin/env python3
"""
配置Webshare代理使用英文地区IP
限制代理只使用英语国家的IP地址

作者: GreenJoson
创建时间: 2025-06-26
"""

import requests
import json
import time

class WebshareEnglishConfig:
    """Webshare英文地区配置器"""

    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://proxy.webshare.io/api/v2"
        self.headers = {
            "Authorization": f"Token {api_key}",
            "Content-Type": "application/json"
        }

        # 英语国家代码列表
        self.english_countries = [
            "US",  # 美国 - 6,652,539 IPs
            "GB",  # 英国 - 1,573,702 IPs
            "CA",  # 加拿大 - 323,485 IPs
            "AU",  # 澳大利亚 - 679,078 IPs
            "NZ",  # 新西兰 - 18,786 IPs
            "IE",  # 爱尔兰 - 27,277 IPs
            "ZA",  # 南非 - 505,795 IPs (英语广泛使用)
        ]

    def get_current_config(self):
        """获取当前代理配置"""
        try:
            response = requests.get(
                f"{self.base_url}/proxy/config/",
                headers=self.headers,
                timeout=10
            )

            if response.status_code == 200:
                return {"success": True, "data": response.json()}
            else:
                return {"success": False, "error": f"HTTP {response.status_code}"}

        except Exception as e:
            return {"success": False, "error": str(e)}

    def calculate_english_ips(self, countries_data):
        """计算英文地区IP数量"""
        english_ips = {}
        total_english = 0

        for country_code in self.english_countries:
            count = countries_data.get(country_code, 0)
            if count > 0:
                english_ips[country_code] = count
                total_english += count

        return english_ips, total_english

    def set_country_restriction(self, country_codes):
        """设置国家限制"""
        try:
            # 更新配置 - 使用正确的API端点
            update_data = {
                "ip_authorization_country_codes": country_codes
            }

            response = requests.patch(
                f"{self.base_url}/proxy/config/",
                headers=self.headers,
                json=update_data,
                timeout=15
            )

            if response.status_code == 200:
                return {"success": True, "data": response.json()}
            else:
                return {"success": False, "error": f"HTTP {response.status_code}", "details": response.text}

        except Exception as e:
            return {"success": False, "error": str(e)}

    def test_english_proxy(self):
        """测试英文地区代理"""
        print("🧪 测试英文地区代理...")

        username = "gorkpiln"
        password = "6lwffvf16jjn"
        proxy_url = f"http://{username}:{password}@p.webshare.io:80"

        proxies = {
            "http": proxy_url,
            "https": proxy_url
        }

        test_results = []

        for i in range(3):
            try:
                print(f"  测试 {i+1}/3...")

                # 获取IP地理信息
                response = requests.get(
                    "http://ip-api.com/json/",
                    proxies=proxies,
                    timeout=20,
                    headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                )

                if response.status_code == 200:
                    data = response.json()
                    result = {
                        "ip": data.get("query"),
                        "country": data.get("country"),
                        "countryCode": data.get("countryCode"),
                        "city": data.get("city"),
                        "region": data.get("regionName"),
                        "timezone": data.get("timezone"),
                        "is_english": data.get("countryCode") in self.english_countries
                    }
                    test_results.append(result)

                    status = "✅" if result["is_english"] else "⚠️"
                    print(f"    {status} {result['ip']} - {result['city']}, {result['country']}")

                else:
                    print(f"    ❌ 请求失败 - 状态码: {response.status_code}")

            except Exception as e:
                print(f"    ❌ 测试异常: {str(e)[:60]}...")

            time.sleep(3)  # 等待IP轮换

        return test_results

    def show_english_countries_info(self, countries_data):
        """显示英文国家信息"""
        print("🇺🇸 英语国家IP分布:")
        print("=" * 50)

        english_ips, total_english = self.calculate_english_ips(countries_data)

        country_names = {
            "US": "美国 (United States)",
            "GB": "英国 (United Kingdom)",
            "CA": "加拿大 (Canada)",
            "AU": "澳大利亚 (Australia)",
            "NZ": "新西兰 (New Zealand)",
            "IE": "爱尔兰 (Ireland)",
            "ZA": "南非 (South Africa)"
        }

        # 按IP数量排序
        sorted_countries = sorted(english_ips.items(), key=lambda x: x[1], reverse=True)

        for country_code, count in sorted_countries:
            country_name = country_names.get(country_code, country_code)
            percentage = (count / total_english) * 100
            print(f"  {country_name}: {count:,} IPs ({percentage:.1f}%)")

        total_all = sum(countries_data.values())
        english_percentage = (total_english / total_all) * 100

        print(f"\n📊 英语国家总计: {total_english:,} IPs")
        print(f"📈 占总代理的: {english_percentage:.1f}%")

        return english_ips, total_english

def main():
    """主函数"""
    print("🇺🇸 Webshare英文地区代理配置工具")
    print("=" * 60)

    api_key = "ulul4v9sr44nvbd2a0v3yhyzpzs2dfgvkdcsnoyg"
    configurator = WebshareEnglishConfig(api_key)

    # 1. 获取当前配置
    print("📡 获取当前代理配置...")
    config_result = configurator.get_current_config()

    if not config_result["success"]:
        print(f"❌ 获取配置失败: {config_result['error']}")
        return

    config_data = config_result["data"]
    countries_data = config_data.get("countries", {})

    # 2. 显示英文国家信息
    english_ips, total_english = configurator.show_english_countries_info(countries_data)

    # 3. 检查当前限制
    current_restrictions = config_data.get("ip_authorization_country_codes")
    print(f"\n🔧 当前国家限制: {current_restrictions or '无限制'}")

    # 4. 询问是否设置英文地区限制
    print(f"\n💡 配置选项:")
    print(f"1. 限制为英语国家 ({total_english:,} IPs)")
    print(f"2. 仅美国+英国+加拿大+澳洲 (主要英语国家)")
    print(f"3. 仅美国 (最多IP: {countries_data.get('US', 0):,})")
    print(f"4. 保持当前设置 (全球轮换)")

    # 自动选择最佳配置
    print(f"\n🎯 推荐配置: 选项2 - 主要英语国家")
    print(f"   包含: 美国、英国、加拿大、澳大利亚")

    main_english_countries = ["US", "GB", "CA", "AU"]
    main_english_total = sum(countries_data.get(code, 0) for code in main_english_countries)

    print(f"   总IP数: {main_english_total:,}")
    print(f"   优势: YouTube访问稳定，时区覆盖全面")

    # 5. 应用配置
    print(f"\n⚙️ 应用英文地区配置...")

    result = configurator.set_country_restriction(main_english_countries)

    if result["success"]:
        print("✅ 英文地区限制设置成功!")
        print(f"   限制国家: {', '.join(main_english_countries)}")
        print(f"   可用IP数: {main_english_total:,}")

        # 6. 测试新配置
        print(f"\n🧪 测试新配置 (需要等待几分钟生效)...")
        print("   注意: 配置更改可能需要5-10分钟生效")

        # 更新本地配置文件
        update_local_config(main_english_countries)

    else:
        print(f"❌ 设置失败: {result['error']}")
        if "details" in result:
            print(f"   详情: {result['details']}")

def update_local_config(country_codes):
    """更新本地配置文件"""
    print(f"\n📝 更新本地配置...")

    # 更新.env文件
    env_content = f"""# Webshare.io 英文地区代理配置
# 更新时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
# 限制国家: {', '.join(country_codes)}

# Webshare 代理认证信息
WEBSHARE_USERNAME=gorkpiln
WEBSHARE_PASSWORD=6lwffvf16jjn
WEBSHARE_ENDPOINT=p.webshare.io:80

# 代理配置
PROXY_USERNAME=gorkpiln
PROXY_PASSWORD=6lwffvf16jjn
ENABLE_PROXY=true
PROXY_TIMEOUT=25

# 英文地区限制
PROXY_COUNTRIES={','.join(country_codes)}

# 爬虫配置 - 针对英文地区优化
MAX_CHANNELS_PER_RUN=60
REQUEST_DELAY_MIN=3
REQUEST_DELAY_MAX=6
CONCURRENT_REQUESTS=3

# 日志级别
LOG_LEVEL=INFO

# 数据库配置
DATABASE_URL=sqlite:///youtube_channels.db
"""

    with open(".env", "w", encoding="utf-8") as f:
        f.write(env_content)

    print("  ✅ .env 文件已更新")

    # 更新proxy_list.txt注释
    with open("proxy_list.txt", "r", encoding="utf-8") as f:
        content = f.read()

    updated_content = f"""# Webshare.io 英文地区代理配置
# 更新时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
# 限制国家: {', '.join(country_codes)}
# 用户名: gorkpiln
# 密码: 6lwffvf16jjn

# Webshare Rotating Residential 代理 (英文地区)
gorkpiln:<EMAIL>:80
"""

    with open("proxy_list.txt", "w", encoding="utf-8") as f:
        f.write(updated_content)

    print("  ✅ proxy_list.txt 已更新")

    print(f"\n🎉 配置完成!")
    print(f"📝 下一步:")
    print(f"1. 等待5-10分钟让配置生效")
    print(f"2. 关闭VPN重新测试: python test_correct_proxy.py")
    print(f"3. 或先用无代理模式: python test_no_proxy.py")
    print(f"4. 开始爬取: python main.py --mode once")

if __name__ == '__main__':
    main()
