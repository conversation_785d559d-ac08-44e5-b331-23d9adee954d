#!/usr/bin/env python3
"""
快速代理测试脚本
诊断Webshare代理连接问题

作者: GreenJoson
创建时间: 2025-06-26
"""

import requests
import time


def test_direct_connection():
    """测试直接网络连接"""
    print("🌐 测试直接网络连接...")
    try:
        response = requests.get("https://httpbin.org/ip", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 直接连接成功 - 本地IP: {data.get('origin')}")
            return True
        else:
            print(f"❌ 直接连接失败 - 状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 直接连接异常: {e}")
        return False


def test_webshare_formats():
    """测试不同的Webshare代理格式"""
    print("\n🔍 测试不同的代理格式...")
    
    # 测试数据
    username = "gorkpiln-1"
    password = "6lwffvf16jjn222"
    
    # 不同的代理格式
    formats = [
        # 格式1: 标准HTTP代理
        {
            "name": "标准HTTP格式",
            "proxies": {
                "http": f"http://{username}:{password}@p.webshare.io:80",
                "https": f"http://{username}:{password}@p.webshare.io:80"
            }
        },
        # 格式2: 尝试HTTPS
        {
            "name": "HTTPS格式",
            "proxies": {
                "http": f"https://{username}:{password}@p.webshare.io:80",
                "https": f"https://{username}:{password}@p.webshare.io:80"
            }
        },
        # 格式3: 不同端口
        {
            "name": "端口8080",
            "proxies": {
                "http": f"http://{username}:{password}@p.webshare.io:8080",
                "https": f"http://{username}:{password}@p.webshare.io:8080"
            }
        },
        # 格式4: rotating-residential端点
        {
            "name": "Rotating端点",
            "proxies": {
                "http": f"http://{username}:{password}@rotating-residential.webshare.io:80",
                "https": f"http://{username}:{password}@rotating-residential.webshare.io:80"
            }
        }
    ]
    
    for i, format_test in enumerate(formats, 1):
        print(f"\n[{i}/4] 测试 {format_test['name']}:")
        print(f"  代理: {format_test['proxies']['http']}")
        
        try:
            response = requests.get(
                "https://httpbin.org/ip",
                proxies=format_test['proxies'],
                timeout=15,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"  ✅ 成功 - 代理IP: {data.get('origin')}")
                return format_test['proxies']  # 返回成功的格式
            else:
                print(f"  ❌ 失败 - 状态码: {response.status_code}")
                
        except requests.exceptions.ProxyError as e:
            print(f"  ❌ 代理错误: {e}")
        except requests.exceptions.Timeout:
            print(f"  ❌ 连接超时")
        except requests.exceptions.ConnectionError as e:
            print(f"  ❌ 连接错误: {e}")
        except Exception as e:
            print(f"  ❌ 其他错误: {e}")
        
        time.sleep(2)  # 避免请求过快
    
    return None


def test_webshare_auth():
    """测试Webshare认证"""
    print("\n🔐 测试Webshare认证...")
    
    # 尝试访问Webshare API
    try:
        # 这个端点通常用于测试认证
        response = requests.get(
            "https://proxy.webshare.io/api/v2/proxy/list/",
            auth=("gorkpiln-1", "6lwffvf16jjn222"),
            timeout=10
        )
        
        print(f"API响应状态码: {response.status_code}")
        if response.status_code == 401:
            print("❌ 认证失败 - 用户名或密码错误")
        elif response.status_code == 200:
            print("✅ 认证成功")
        else:
            print(f"⚠️  未知状态: {response.status_code}")
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")


def check_webshare_status():
    """检查Webshare服务状态"""
    print("\n📊 检查Webshare服务状态...")
    
    endpoints = [
        "https://webshare.io",
        "https://proxy.webshare.io",
        "http://p.webshare.io",
        "http://rotating-residential.webshare.io"
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(endpoint, timeout=10)
            print(f"  {endpoint}: ✅ 可访问 (状态码: {response.status_code})")
        except Exception as e:
            print(f"  {endpoint}: ❌ 不可访问 ({e})")


def main():
    """主函数"""
    print("🔧 Webshare代理连接诊断工具")
    print("=" * 50)
    
    # 1. 测试直接连接
    direct_ok = test_direct_connection()
    
    if not direct_ok:
        print("\n❌ 网络连接有问题，请检查网络设置")
        return
    
    # 2. 检查Webshare服务状态
    check_webshare_status()
    
    # 3. 测试认证
    test_webshare_auth()
    
    # 4. 测试不同代理格式
    working_format = test_webshare_formats()
    
    print("\n" + "=" * 50)
    print("📋 诊断结果:")
    
    if working_format:
        print("✅ 找到可用的代理格式！")
        print(f"   推荐格式: {working_format['http']}")
        print("\n📝 下一步:")
        print("1. 更新 proxy_list.txt 使用正确的格式")
        print("2. 运行: python switch_config.py residential")
        print("3. 开始爬取: python main.py --mode once")
    else:
        print("❌ 所有代理格式都失败了")
        print("\n🔍 可能的原因:")
        print("1. 代理账户未激活或已过期")
        print("2. 用户名/密码错误")
        print("3. IP白名单限制（如果启用了）")
        print("4. 代理服务器维护中")
        print("\n💡 建议:")
        print("1. 登录Webshare控制台检查账户状态")
        print("2. 确认代理套餐是否已激活")
        print("3. 检查IP白名单设置")
        print("4. 联系Webshare客服支持")


if __name__ == '__main__':
    main()
