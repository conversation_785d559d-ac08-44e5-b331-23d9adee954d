#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SEO分析器 v1.0
批量查询域名在站长之家的SEO信息
"""

import requests
import re
import time
import random
import json
import csv
from datetime import datetime
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from urllib.parse import urljoin

class SEOAnalyzer:
    def __init__(self):
        self.results = []
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        })

    def get_seo_info(self, domain):
        """获取单个域名的SEO信息"""
        url = f"https://seo.chinaz.com/{domain}"

        try:
            print(f"🔍 查询: {domain}")

            # 随机延时避免被限制
            time.sleep(random.uniform(2, 5))

            response = self.session.get(url, timeout=15)
            response.raise_for_status()

            html = response.text

            # 解析SEO信息
            seo_info = self._parse_seo_info(domain, html)

            if seo_info['title']:
                print(f"  ✅ {domain} - {seo_info['title'][:30]}...")
            else:
                print(f"  ⚠️  {domain} - 无标题信息")

            return seo_info

        except requests.exceptions.RequestException as e:
            print(f"  ❌ {domain} - 请求失败: {e}")
            return self._get_empty_info(domain, f"请求失败: {e}")
        except Exception as e:
            print(f"  ❌ {domain} - 解析失败: {e}")
            return self._get_empty_info(domain, f"解析失败: {e}")

    def _parse_seo_info(self, domain, html):
        """解析HTML中的SEO信息"""
        info = {
            'domain': domain,
            'title': '',
            'baidu_pc_rank': 0,
            'baidu_mobile_rank': 0,
            'sogou_rank': 0,
            'bing_rank': 0,
            'shenma_rank': 0,
            'so360_rank': 0,
            'pc_keywords': 0,
            'mobile_keywords': 0,
            'baidu_index': '',
            'backlinks': '',
            'total_traffic': '',
            'category': '',
            'domain_age': '',
            'ip_address': '',
            'registrar': '',
            'checked_at': datetime.now().isoformat(),
            'error': ''
        }

        try:
            # 提取网站标题
            title_match = re.search(r'<div class="_chinaz-seo-t2l"[^>]*>([^<]+)', html)
            if title_match:
                info['title'] = title_match.group(1).strip()

            # 提取权重信息 (通过data-rank属性)
            # 百度PC权重
            baidu_pc_match = re.search(r'百度PC.*?data-rank="(\d+)"', html, re.DOTALL)
            if baidu_pc_match:
                info['baidu_pc_rank'] = int(baidu_pc_match.group(1))

            # 百度移动权重
            baidu_mobile_match = re.search(r'百度移动.*?data-rank="(\d+)"', html, re.DOTALL)
            if baidu_mobile_match:
                info['baidu_mobile_rank'] = int(baidu_mobile_match.group(1))

            # 搜狗权重
            sogou_match = re.search(r'搜狗.*?data-rank="(\d+)"', html, re.DOTALL)
            if sogou_match:
                info['sogou_rank'] = int(sogou_match.group(1))

            # 必应权重
            bing_match = re.search(r'必应.*?data-rank="(\d+)"', html, re.DOTALL)
            if bing_match:
                info['bing_rank'] = int(bing_match.group(1))

            # 神马权重
            shenma_match = re.search(r'神马.*?data-rank="(\d+)"', html, re.DOTALL)
            if shenma_match:
                info['shenma_rank'] = int(shenma_match.group(1))

            # 360权重
            so360_match = re.search(r'360.*?data-rank="(\d+)"', html, re.DOTALL)
            if so360_match:
                info['so360_rank'] = int(so360_match.group(1))

            # 提取关键词数量
            # PC词数
            pc_kw_match = re.search(r'<div class="Ma01LiRow w10-7 pckwcount"><a[^>]*>(\d+)</a>', html)
            if pc_kw_match:
                info['pc_keywords'] = int(pc_kw_match.group(1))

            # 移动词数
            mobile_kw_match = re.search(r'<div class="Ma01LiRow w10-7 mobilekwcount"><a[^>]*>(\d+)</a>', html)
            if mobile_kw_match:
                info['mobile_keywords'] = int(mobile_kw_match.group(1))

            # 提取总流量
            traffic_match = re.search(r'全网流量总和：<i[^>]*><a[^>]*>([^<]+)</a>', html)
            if traffic_match:
                info['total_traffic'] = traffic_match.group(1).strip()

            # 提取百度收录量
            index_match = re.search(r'<span id="seo_BaiduSiteIndex"><a[^>]*>([^<]+)</a>', html)
            if index_match:
                info['baidu_index'] = index_match.group(1).strip()

            # 提取反链数
            backlink_match = re.search(r'<div class="Ma01LiRow w10-7 outlinkcount"><a[^>]*>(\d+)</a>', html)
            if backlink_match:
                info['backlinks'] = backlink_match.group(1)

            # 提取网站分类
            category_match = re.search(r'网站分类：<i class="color-63">([^<]+)</i>', html)
            if category_match:
                info['category'] = category_match.group(1).strip()

            # 提取域名年龄
            age_match = re.search(r'域名年龄：.*?<i class="color-63">([^<]+)</i>', html, re.DOTALL)
            if age_match:
                info['domain_age'] = age_match.group(1).strip()

            # 提取IP地址
            ip_match = re.search(r'IP：<i class="color-63"><a[^>]*>([^<]+)</a>', html)
            if ip_match:
                info['ip_address'] = ip_match.group(1).strip()

            # 提取注册商
            registrar_match = re.search(r'注册人/机构：\s*<i class="color-63">\s*([^<]+)\s*</i>', html)
            if registrar_match:
                info['registrar'] = registrar_match.group(1).strip()

        except Exception as e:
            info['error'] = f"解析错误: {e}"

        return info

    def _get_empty_info(self, domain, error=""):
        """返回空的域名信息"""
        return {
            'domain': domain,
            'title': '',
            'baidu_pc_rank': 0,
            'baidu_mobile_rank': 0,
            'sogou_rank': 0,
            'bing_rank': 0,
            'shenma_rank': 0,
            'so360_rank': 0,
            'pc_keywords': 0,
            'mobile_keywords': 0,
            'baidu_index': '',
            'backlinks': '',
            'total_traffic': '',
            'category': '',
            'domain_age': '',
            'ip_address': '',
            'registrar': '',
            'checked_at': datetime.now().isoformat(),
            'error': error
        }

    def batch_analyze(self, domains, max_workers=3):
        """批量分析域名SEO信息"""
        print(f"🚀 开始批量分析 {len(domains)} 个域名...")
        print(f"并发数: {max_workers}")
        print("=" * 60)

        self.results = []

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_domain = {
                executor.submit(self.get_seo_info, domain): domain
                for domain in domains
            }

            completed = 0
            for future in as_completed(future_to_domain):
                try:
                    result = future.result()
                    self.results.append(result)
                    completed += 1

                    # 每完成10个显示进度
                    if completed % 10 == 0:
                        print(f"📊 进度: {completed}/{len(domains)}")

                except Exception as e:
                    domain = future_to_domain[future]
                    print(f"❌ {domain} - 处理异常: {e}")
                    self.results.append(self._get_empty_info(domain, f"处理异常: {e}"))

        print(f"\n✅ 分析完成！")
        return self.results

    def load_domains_from_file(self, filename):
        """从文件加载域名列表"""
        domains = []
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                for line in f:
                    domain = line.strip()
                    if domain and not domain.startswith('#'):
                        # 清理域名格式
                        domain = domain.replace('http://', '').replace('https://', '')
                        domain = domain.split('/')[0]  # 去掉路径部分
                        if domain:
                            domains.append(domain)
            print(f"📄 从 {filename} 加载了 {len(domains)} 个域名")
            return domains
        except FileNotFoundError:
            print(f"❌ 文件 {filename} 不存在")
            return []

    def filter_valuable_domains(self, min_rank=1, min_keywords=5):
        """筛选有价值的域名"""
        if not self.results:
            print("❌ 没有分析结果")
            return []

        valuable = []
        for result in self.results:
            # 判断是否有价值
            has_rank = (result['baidu_pc_rank'] >= min_rank or
                       result['baidu_mobile_rank'] >= min_rank or
                       result['sogou_rank'] >= min_rank)

            has_keywords = (result['pc_keywords'] >= min_keywords or
                           result['mobile_keywords'] >= min_keywords)

            if has_rank or has_keywords or result['title']:
                valuable.append(result)

        print(f"📈 筛选出 {len(valuable)} 个有价值的域名")
        return valuable

    def save_results(self, prefix="seo_analysis"):
        """保存分析结果"""
        if not self.results:
            print("❌ 没有结果可保存")
            return

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # 保存详细JSON结果
        json_file = f"{prefix}_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        print(f"📄 详细结果已保存到: {json_file}")

        # 保存CSV报告
        csv_file = f"{prefix}_{timestamp}.csv"
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow([
                '域名', '网站标题', '百度PC权重', '百度移动权重', '搜狗权重', '必应权重',
                '神马权重', '360权重', 'PC关键词数', '移动关键词数', '百度收录量',
                '反链数', '总流量', '网站分类', '域名年龄', 'IP地址', '注册商', '错误信息'
            ])

            for result in self.results:
                writer.writerow([
                    result['domain'], result['title'], result['baidu_pc_rank'],
                    result['baidu_mobile_rank'], result['sogou_rank'], result['bing_rank'],
                    result['shenma_rank'], result['so360_rank'], result['pc_keywords'],
                    result['mobile_keywords'], result['baidu_index'], result['backlinks'],
                    result['total_traffic'], result['category'], result['domain_age'],
                    result['ip_address'], result['registrar'], result['error']
                ])

        print(f"📄 CSV报告已保存到: {csv_file}")

        # 保存有价值域名列表
        valuable = self.filter_valuable_domains()
        if valuable:
            valuable_file = f"valuable_domains_{timestamp}.txt"
            with open(valuable_file, 'w', encoding='utf-8') as f:
                f.write(f"# 有价值域名列表 - {timestamp}\n")
                f.write(f"# 共 {len(valuable)} 个\n\n")
                for result in valuable:
                    ranks = []
                    if result['baidu_pc_rank'] > 0:
                        ranks.append(f"百度PC:{result['baidu_pc_rank']}")
                    if result['baidu_mobile_rank'] > 0:
                        ranks.append(f"百度移动:{result['baidu_mobile_rank']}")
                    if result['pc_keywords'] > 0:
                        ranks.append(f"PC词:{result['pc_keywords']}")
                    if result['mobile_keywords'] > 0:
                        ranks.append(f"移动词:{result['mobile_keywords']}")

                    rank_info = " | ".join(ranks) if ranks else "有标题"
                    f.write(f"{result['domain']} - {rank_info} - {result['title'][:50]}\n")

            print(f"📄 有价值域名已保存到: {valuable_file}")

    def print_summary(self):
        """打印分析摘要"""
        if not self.results:
            print("❌ 没有分析结果")
            return

        total = len(self.results)
        has_title = len([r for r in self.results if r['title']])
        has_rank = len([r for r in self.results if r['baidu_pc_rank'] > 0 or r['baidu_mobile_rank'] > 0])
        has_keywords = len([r for r in self.results if r['pc_keywords'] > 0 or r['mobile_keywords'] > 0])
        errors = len([r for r in self.results if r['error']])

        print(f"\n📊 SEO分析摘要:")
        print(f"  总域名数: {total}")
        print(f"  有标题: {has_title} 个")
        print(f"  有权重: {has_rank} 个")
        print(f"  有关键词: {has_keywords} 个")
        print(f"  查询失败: {errors} 个")

        # 显示排名最高的域名
        ranked_domains = [r for r in self.results if r['baidu_pc_rank'] > 0 or r['baidu_mobile_rank'] > 0]
        if ranked_domains:
            ranked_domains.sort(key=lambda x: max(x['baidu_pc_rank'], x['baidu_mobile_rank']), reverse=True)
            print(f"\n🏆 权重最高的域名:")
            for i, result in enumerate(ranked_domains[:10]):
                max_rank = max(result['baidu_pc_rank'], result['baidu_mobile_rank'])
                print(f"  {i+1:2d}. {result['domain']:20} - 权重{max_rank} - {result['title'][:30]}")

def main():
    print("📊 SEO分析器 v1.0")
    print("=" * 50)

    analyzer = SEOAnalyzer()

    # 选择输入方式
    print("请选择域名输入方式:")
    print("1. 从文件加载域名列表")
    print("2. 手动输入域名")

    choice = input("选择 (1/2): ").strip()

    domains = []

    if choice == "1":
        # 查找可能的域名文件
        import glob
        domain_files = (
            glob.glob("available_domains_*.txt") +
            glob.glob("candidate_domains_*.txt") +
            glob.glob("enhanced_candidates_*.txt")
        )

        if domain_files:
            print(f"\n发现域名文件:")
            for i, file in enumerate(domain_files):
                print(f"  {i+1}. {file}")

            file_choice = input(f"选择文件 (1-{len(domain_files)}) 或输入文件名: ").strip()

            if file_choice.isdigit() and 1 <= int(file_choice) <= len(domain_files):
                filename = domain_files[int(file_choice) - 1]
            else:
                filename = file_choice
        else:
            filename = input("输入域名文件名: ").strip()

        domains = analyzer.load_domains_from_file(filename)

    elif choice == "2":
        print("请输入域名 (每行一个，输入空行结束):")
        while True:
            domain = input("域名: ").strip()
            if not domain:
                break
            domains.append(domain)

    if not domains:
        print("❌ 没有要分析的域名")
        return

    # 设置分析参数
    print(f"\n⚙️  分析设置:")

    # 限制分析数量
    if len(domains) > 50:
        limit = input(f"域名较多({len(domains)}个)，建议限制数量 (默认50): ").strip()
        if limit.isdigit():
            domains = domains[:int(limit)]
        else:
            domains = domains[:50]

    # 设置并发数
    max_workers = 2  # 默认较低的并发避免被限制
    worker_input = input(f"并发数 (默认: {max_workers}, 建议1-3): ").strip()
    if worker_input.isdigit():
        max_workers = min(int(worker_input), 5)  # 限制最大并发数

    # 开始分析
    print(f"\n🚀 开始SEO分析...")
    print(f"⚠️  注意: 查询间隔较长以避免被限制，请耐心等待")

    analyzer.batch_analyze(domains, max_workers=max_workers)

    # 显示结果
    analyzer.print_summary()

    # 保存结果
    analyzer.save_results()

    print(f"\n✅ SEO分析完成！")

if __name__ == "__main__":
    main()