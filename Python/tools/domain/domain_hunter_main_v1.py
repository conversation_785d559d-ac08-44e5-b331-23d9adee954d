#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电影站域名猎手 - 主控制器 v1.0
整合域名发现、分析和可用性检查的完整流程
"""

import os
import sys
import json
import time
from datetime import datetime

class DomainHunterMain:
    def __init__(self):
        self.session_id = datetime.now().strftime('%Y%m%d_%H%M%S')
        print(f"🎬 电影站域名猎手 v1.0")
        print(f"会话ID: {self.session_id}")
        print("=" * 60)

    def check_requirements(self):
        """检查必要文件和工具"""
        print("🔧 检查运行环境...")

        # 检查wayback.txt文件
        if not os.path.exists('wayback.txt'):
            print("❌ 缺少wayback.txt文件")
            print("请先从Wayback Machine搜索页面保存HTML到wayback.txt")
            return False

        # 检查提取器脚本
        if not os.path.exists('enhanced_domain_extractor_v1.py'):
            print("❌ 缺少enhanced_domain_extractor_v1.py")
            return False

        # 检查可用性检查器
        if not os.path.exists('domain_availability_checker_v1.py'):
            print("❌ 缺少domain_availability_checker_v1.py")
            return False

        # 检查SEO分析器
        if not os.path.exists('seo_analyzer_v1.py'):
            print("⚠️  缺少seo_analyzer_v1.py (SEO分析功能不可用)")

        print("✅ 环境检查通过")
        return True

    def run_domain_extraction(self):
        """运行域名提取"""
        print(f"\n📊 步骤1: 提取电影站域名")
        print("-" * 40)

        # 运行域名提取器
        exit_code = os.system('python3 enhanced_domain_extractor_v1.py')

        if exit_code != 0:
            print("❌ 域名提取失败")
            return None

                # 查找生成的候选域名文件
        import glob
        candidate_files = glob.glob("enhanced_candidates_*.txt")
        if not candidate_files:
            # 如果没有找到enhanced文件，尝试找旧的文件
            candidate_files = glob.glob("candidate_domains_*.txt")

        if not candidate_files:
            print("❌ 没有找到生成的候选域名文件")
            return None

        # 选择最新的文件
        latest_file = max(candidate_files, key=os.path.getctime)
        print(f"✅ 域名提取完成，候选文件: {latest_file}")
        return latest_file

    def run_availability_check(self, domains_file, sample_size=None):
        """运行可用性检查"""
        print(f"\n🔍 步骤2: 检查域名可用性")
        print("-" * 40)

        # 读取域名列表
        try:
            with open(domains_file, 'r', encoding='utf-8') as f:
                domains = [line.strip() for line in f if line.strip()]
        except Exception as e:
            print(f"❌ 读取域名文件失败: {e}")
            return None

        print(f"📄 加载了 {len(domains)} 个候选域名")

        # 如果域名太多，进行采样
        if sample_size and len(domains) > sample_size:
            import random
            domains = random.sample(domains, sample_size)
            print(f"🎲 随机采样 {sample_size} 个域名进行检查")

        # 导入并运行检查器
        try:
            from domain_availability_checker_v1 import DomainAvailabilityChecker

            checker = DomainAvailabilityChecker()

            # 使用DNS快速检查
            print("使用DNS快速检查模式...")
            checker.batch_check(domains, max_workers=5, use_whois=False)

            # 保存结果
            checker.save_results(f"session_{self.session_id}")

            # 显示摘要
            checker.print_summary()

            return checker.available_domains

        except Exception as e:
            print(f"❌ 可用性检查失败: {e}")
            return None

    def run_seo_analysis(self):
        """运行SEO分析"""
        print(f"\n📊 SEO分析功能")
        print("-" * 40)

        try:
            # 检查SEO分析器是否存在
            if not os.path.exists('seo_analyzer_v1.py'):
                print("❌ SEO分析器文件不存在")
                return

            # 导入SEO分析器
            sys.path.append('.')
            from seo_analyzer_v1 import SEOAnalyzer

            analyzer = SEOAnalyzer()

            # 查找域名文件
            import glob
            domain_files = (
                glob.glob("available_domains_*.txt") +
                glob.glob("candidate_domains_*.txt") +
                glob.glob("enhanced_candidates_*.txt")
            )

            if not domain_files:
                print("❌ 没有找到域名文件，请先运行域名提取")
                return

            print("发现域名文件:")
            for i, file in enumerate(domain_files[-5:]):  # 显示最新的5个文件
                print(f"  {i+1}. {file}")

            choice = input(f"选择文件 (1-{len(domain_files[-5:])}): ").strip()

            if choice.isdigit() and 1 <= int(choice) <= len(domain_files[-5:]):
                selected_file = domain_files[-5:][int(choice) - 1]
            else:
                selected_file = domain_files[-1]  # 默认选择最新的

            # 加载域名
            domains = analyzer.load_domains_from_file(selected_file)
            if not domains:
                return

            # 分析策略选择
            total_domains = len(domains)
            if total_domains > 50:
                print(f"\n📊 分析策略选择:")
                print(f"1. 限制数量分析 (推荐)")
                print(f"2. 全部分析 ({total_domains}个域名)")
                print(f"3. 分批全部分析 (每批20个)")

                strategy = input(f"选择策略 (1-3, 默认1): ").strip()

                if strategy == "2":
                    # 全部分析
                    confirm = input(f"⚠️  确认分析全部{total_domains}个域名？可能需要很长时间 (y/N): ").strip().lower()
                    if confirm != 'y':
                        domains = domains[:20]
                        print(f"📊 改为分析前20个域名")
                elif strategy == "3":
                    # 分批分析
                    batch_size = 20
                    batch_input = input(f"每批数量 (默认: {batch_size}): ").strip()
                    if batch_input.isdigit():
                        batch_size = min(int(batch_input), 50)

                    print(f"📊 将分{(total_domains + batch_size - 1) // batch_size}批分析，每批{batch_size}个域名")
                    self.batch_seo_analysis(analyzer, domains, batch_size)
                    return
                else:
                    # 限制数量分析
                    limit = input(f"分析数量 (默认20): ").strip()
                    if limit.isdigit():
                        domains = domains[:int(limit)]
                    else:
                        domains = domains[:20]
            elif total_domains > 20:
                limit = input(f"域名较多({total_domains}个)，建议限制数量 (默认20): ").strip()
                if limit.isdigit():
                    domains = domains[:int(limit)]
                else:
                    domains = domains[:20]

            # 设置并发数
            max_workers = 2
            worker_input = input(f"并发数 (默认: {max_workers}, 建议1-3): ").strip()
            if worker_input.isdigit():
                max_workers = min(int(worker_input), 3)

            print(f"\n🚀 开始SEO分析...")
            print(f"📊 分析域名数量: {len(domains)}个")
            print(f"⚠️  查询间隔较长以避免被限制，请耐心等待")

            # 执行分析
            analyzer.batch_analyze(domains, max_workers=max_workers)

            # 显示结果
            analyzer.print_summary()

            # 保存结果
            analyzer.save_results(f"seo_session_{self.session_id}")

            print(f"✅ SEO分析完成！")

        except ImportError as e:
            print(f"❌ 导入SEO分析器失败: {e}")
        except Exception as e:
            print(f"❌ SEO分析失败: {e}")

    def batch_seo_analysis(self, analyzer, domains, batch_size=20):
        """分批SEO分析"""
        total_domains = len(domains)
        total_batches = (total_domains + batch_size - 1) // batch_size

        print(f"\n🚀 开始分批SEO分析...")
        print(f"📊 总域名: {total_domains}个，分{total_batches}批，每批{batch_size}个")
        print(f"⚠️  查询间隔较长以避免被限制，请耐心等待")

        # 合并所有批次结果的分析器
        all_results = []

        for batch_num in range(total_batches):
            start_idx = batch_num * batch_size
            end_idx = min(start_idx + batch_size, total_domains)
            batch_domains = domains[start_idx:end_idx]

            print(f"\n📦 批次 {batch_num + 1}/{total_batches} - 分析 {len(batch_domains)} 个域名")
            print(f"   {start_idx + 1}-{end_idx}: {', '.join(batch_domains[:3])}{'...' if len(batch_domains) > 3 else ''}")

            # 创建临时分析器进行本批分析
            batch_analyzer = analyzer.__class__()

            try:
                # 执行本批分析
                batch_analyzer.batch_analyze(batch_domains, max_workers=2)

                # 收集结果
                all_results.extend(batch_analyzer.results)

                # 显示本批摘要
                batch_analyzer.print_summary()

                # 保存本批结果
                batch_analyzer.save_results(f"seo_batch_{batch_num+1}_{self.session_id}")

                # 批次间休息
                if batch_num < total_batches - 1:
                    print(f"⏱️  批次间休息30秒...")
                    import time
                    time.sleep(30)

            except Exception as e:
                print(f"❌ 批次 {batch_num + 1} 分析失败: {e}")
                continue

        # 合并所有结果到主分析器
        analyzer.results = all_results

        print(f"\n🎊 分批分析完成！")
        print(f"📊 总分析结果:")
        analyzer.print_summary()

        # 保存合并结果
        analyzer.save_results(f"seo_all_batches_{self.session_id}")

        print(f"✅ 所有批次分析完成！共分析 {len(all_results)} 个域名")

    def generate_final_report(self, available_domains):
        """生成最终报告"""
        print(f"\n📋 步骤3: 生成最终报告")
        print("-" * 40)

        if not available_domains:
            print("❌ 没有可用域名，无法生成报告")
            return

        report_file = f"domain_hunting_report_{self.session_id}.md"

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(f"# 电影站域名挖掘报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**会话ID**: {self.session_id}\n\n")

            f.write(f"## 📊 挖掘统计\n\n")
            f.write(f"- 发现可能可注册域名: **{len(available_domains)}** 个\n")
            f.write(f"- 检查方式: DNS快速查询\n")
            f.write(f"- 数据来源: Wayback Machine 电影站搜索\n\n")

            f.write(f"## 🎯 推荐域名\n\n")
            f.write(f"以下域名经过分析可能可以注册:\n\n")

            for i, item in enumerate(available_domains[:20]):  # 显示前20个
                f.write(f"{i+1}. **{item['domain']}**\n")

            if len(available_domains) > 20:
                f.write(f"\n... 还有 {len(available_domains) - 20} 个域名\n")

            f.write(f"\n## ⚠️ 注意事项\n\n")
            f.write(f"1. DNS查询只能初步判断域名状态，建议使用WHOIS确认\n")
            f.write(f"2. 域名可能存在保留期、争议期等特殊状态\n")
            f.write(f"3. 注册前请确认域名没有商标等法律问题\n")
            f.write(f"4. 建议使用正规域名注册商进行注册\n")
            f.write(f"5. 可使用SEO分析功能进一步评估域名价值\n\n")

            f.write(f"## 📁 相关文件\n\n")
            f.write(f"- 详细检查结果: `availability_check_session_{self.session_id}_*.json`\n")
            f.write(f"- 可用域名列表: `available_domains_*.txt`\n")
            f.write(f"- 原始分析数据: `extracted_domains_*.json`\n")
            f.write(f"- SEO分析结果: `seo_session_{self.session_id}_*.json` (如有运行)\n")

        print(f"📄 最终报告已保存到: {report_file}")

    def interactive_menu(self):
        """交互式菜单"""
        while True:
            print(f"\n🎮 选择操作:")
            print("1. 🚀 运行完整挖掘流程")
            print("2. 📊 仅提取域名")
            print("3. 🔍 仅检查可用性")
            print("4. 📈 SEO分析 (新功能)")
            print("5. 📋 查看历史会话")
            print("6. 🔧 工具设置")
            print("0. 退出")

            choice = input("\n请选择 (0-6): ").strip()

            if choice == "0":
                print("👋 再见！")
                break
            elif choice == "1":
                self.run_full_process()
            elif choice == "2":
                self.run_domain_extraction()
            elif choice == "3":
                self.manual_availability_check()
            elif choice == "4":
                self.run_seo_analysis()
            elif choice == "5":
                self.show_history()
            elif choice == "6":
                self.settings_menu()
            else:
                print("❌ 无效选择")

    def run_full_process(self):
        """运行完整挖掘流程"""
        print(f"\n🚀 开始完整域名挖掘流程")
        print("=" * 50)

        # 步骤1: 提取域名
        domains_file = self.run_domain_extraction()
        if not domains_file:
            return

        # 步骤2: 检查可用性
        print(f"\n是否限制检查数量？(域名文件可能包含很多域名)")
        limit_input = input("输入检查数量限制 (回车=全部检查): ").strip()
        sample_size = int(limit_input) if limit_input.isdigit() else None

        available_domains = self.run_availability_check(domains_file, sample_size)
        if available_domains is None:
            return

        # 步骤3: 生成报告
        self.generate_final_report(available_domains)

        # 可选步骤4: SEO分析
        if available_domains:
            seo_choice = input(f"\n是否对可用域名进行SEO分析？(y/N): ").strip().lower()
            if seo_choice == 'y':
                print(f"将对前20个可用域名进行SEO分析...")
                # 创建临时文件供SEO分析
                temp_file = f"temp_available_{self.session_id}.txt"
                with open(temp_file, 'w', encoding='utf-8') as f:
                    for item in available_domains[:20]:
                        f.write(f"{item['domain']}\n")

                # 运行SEO分析
                try:
                    from seo_analyzer_v1 import SEOAnalyzer
                    analyzer = SEOAnalyzer()
                    domains = analyzer.load_domains_from_file(temp_file)
                    if domains:
                        analyzer.batch_analyze(domains, max_workers=2)
                        analyzer.print_summary()
                        analyzer.save_results(f"available_seo_{self.session_id}")

                    # 清理临时文件
                    os.remove(temp_file)
                except Exception as e:
                    print(f"❌ SEO分析失败: {e}")

        print(f"\n🎊 完整流程执行完毕！")
        print(f"发现 {len(available_domains)} 个可能可注册的域名")

    def manual_availability_check(self):
        """手动可用性检查"""
        print(f"\n🔍 手动域名可用性检查")

        # 查找候选文件
        import glob
        candidate_files = glob.glob("candidate_domains_*.txt") + glob.glob("enhanced_candidates_*.txt")

        if candidate_files:
            print(f"\n发现候选文件:")
            for i, file in enumerate(candidate_files):
                print(f"  {i+1}. {file}")

            choice = input(f"选择文件 (1-{len(candidate_files)}): ").strip()
            if choice.isdigit() and 1 <= int(choice) <= len(candidate_files):
                domains_file = candidate_files[int(choice) - 1]
                self.run_availability_check(domains_file)
            else:
                print("❌ 无效选择")
        else:
            print("❌ 没有找到候选域名文件，请先运行域名提取")

    def show_history(self):
        """显示历史会话"""
        print(f"\n📋 历史会话")

        # 查找历史报告
        import glob
        reports = glob.glob("domain_hunting_report_*.md")

        if reports:
            print(f"发现 {len(reports)} 个历史会话:")
            for report in sorted(reports, reverse=True):
                # 提取会话ID
                session_id = report.replace('domain_hunting_report_', '').replace('.md', '')
                print(f"  - 会话 {session_id}")

            choice = input(f"\n输入会话ID查看详情 (回车=返回): ").strip()
            if choice:
                report_file = f"domain_hunting_report_{choice}.md"
                if os.path.exists(report_file):
                    with open(report_file, 'r', encoding='utf-8') as f:
                        print(f"\n{f.read()}")
                else:
                    print("❌ 会话不存在")
        else:
            print("📭 没有历史会话")

    def settings_menu(self):
        """设置菜单"""
        print(f"\n🔧 工具设置")
        print("1. 清理临时文件")
        print("2. 检查依赖")
        print("3. 更新工具")

        choice = input("选择操作: ").strip()

        if choice == "1":
            # 清理临时文件
            import glob
            temp_files = (
                glob.glob("extracted_domains_*.json") +
                glob.glob("candidate_domains_*.txt") +
                glob.glob("enhanced_candidates_*.txt") +
                glob.glob("availability_check_*.json") +
                glob.glob("available_domains_*.txt") +
                glob.glob("domain_check_report_*.csv") +
                glob.glob("seo_analysis_*.json") +
                glob.glob("seo_analysis_*.csv") +
                glob.glob("valuable_domains_*.txt") +
                glob.glob("temp_available_*.txt")
            )

            if temp_files:
                print(f"发现 {len(temp_files)} 个临时文件")
                confirm = input("确认删除？(y/N): ").strip().lower()
                if confirm == 'y':
                    for file in temp_files:
                        os.remove(file)
                    print(f"✅ 已删除 {len(temp_files)} 个临时文件")
            else:
                print("没有临时文件需要清理")

        elif choice == "2":
            self.check_requirements()

        elif choice == "3":
            print("请手动检查GitHub或其他来源的工具更新")

def main():
    hunter = DomainHunterMain()

    # 检查环境
    if not hunter.check_requirements():
        print("\n请解决上述问题后重新运行")
        return

    # 启动交互菜单
    hunter.interactive_menu()

if __name__ == "__main__":
    main()